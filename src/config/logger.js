import { isEnv } from "helpers/utils";
import winston from "winston";
import DailyRotateFile from "winston-daily-rotate-file";

const { combine, errors, timestamp, json, prettyPrint } = winston.format;

const newRelicTransport = new winston.transports.Http({
    host: "log-api.eu.newrelic.com",
    path: "/log/v1",
    ssl: true,
    headers: {
        "Content-Type": "application/json",
        "X-License-Key": process.env.NEW_RELIC_KEY
    }
});

const fileTransport = (prefix) =>
    new DailyRotateFile({
        filename: `logs/${prefix}-%DATE%.log`,
        datePattern: "DD-MM-YYYY",
        zippedArchive: true,
        maxSize: "10m",
        maxFiles: "14d"
    });

const configureTransports = (prefix) => {
    const envs = "production";
    return [
        !isEnv(envs) && [].concat(new winston.transports.Console(), fileTransport(prefix)),
        isEnv(envs) && newRelicTransport
    ]
        .flatMap((elem) => elem)
        .filter(Boolean);
};

// TODO: Redact sensitive info
const Log = winston.createLogger({
    format: combine(
        errors({ stack: true }),
        timestamp({ format: "DD-MM-YYYY HH:mm:ss", tz: "Africa/Lagos" }),
        json(),
        prettyPrint()
    ),
    transports: configureTransports("info"),
    exceptionHandlers: configureTransports("exceptions"),
    rejectionHandlers: configureTransports("rejections")
});

export default Log;
