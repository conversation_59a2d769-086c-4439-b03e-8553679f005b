import connectRedis from "connect-redis";
import expressSession from "express-session";
import { redis } from "config/redis";

const RedisStore = connectRedis(expressSession);

const { NODE_ENV, SESSION_SECRET } = process.env;

export const session = expressSession({
    name: "indigov2",
    secret: SESSION_SECRET,
    saveUninitialized: false,
    resave: false,
    store: new RedisStore({ client: redis }),
    cookie: {
        sameSite: NODE_ENV === "local" ? "lax" : "none",
        secure: NODE_ENV !== "local"
    }
});
