import { beforeAll } from "@jest/globals";
import { redis } from "config/redis";
import mongoose from "mongoose";
import throwErrorWithCode from "utils/throw-error";

export default async () => {
    beforeAll(async () => {
        await redis.connect();
        await mongoose.connect(global.__MONGO_URI__, {}, (err) => {
            if (err) throwErrorWithCode(err?.message, 500);
        });
    });

    afterAll(async () => {
        await mongoose.disconnect();
        await redis.quit(); // Properly close the Redis connection
    });
};
