/* eslint-disable import/no-cycle */
import Agenda from "agenda";
import admissionDefinition from "utils/jobs/admission";
import cardDefinition from "utils/jobs/card";
import emailDefinition from "utils/jobs/email";
import licenceDefinition from "utils/jobs/license";
import miscDefinition from "utils/jobs/misc";
import notificationDefinition from "utils/jobs/notification";
import organizationTransactionDefinition from "utils/jobs/organization-transaction";
import smsDefinition from "utils/jobs/sms";
import { autoCreditOrganizationDefinition } from "utils/jobs/subscription";
import triggerSmsDefinition from "utils/jobs/trigger-sms";
import paymentDefinition from "utils/jobs/payment";
import releaseNoteDefinition from "utils/jobs/release-note";

const { DATABASE_URL } = process.env;

const agenda = new Agenda({
    db: { address: DATABASE_URL, collection: "cronjobs", lockLimit: 15 }
});

agenda
    .on("ready", () => console.log("Agenda started!"))
    .on("error", (err) => console.log("Agenda connection error!", err?.message));

const definitions = [
    emailDefinition,
    notificationDefinition,
    smsDefinition,
    organizationTransactionDefinition,
    licenceDefinition,
    autoCreditOrganizationDefinition,
    cardDefinition,
    miscDefinition,
    triggerSmsDefinition,
    admissionDefinition,
    paymentDefinition,
    releaseNoteDefinition
];

definitions.forEach((definition) => definition(agenda));

(async () => {
    await agenda.start();
})();

export default agenda;
