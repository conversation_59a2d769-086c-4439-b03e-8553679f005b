import { SES } from "@aws-sdk/client-ses";
import nodemailer from "nodemailer";
import mailgunTransport from "nodemailer-mailgun-transport";

const { MAILGUN_API_KEY, MAILGUN_DOMAIN, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION } = process.env;
const region = AWS_REGION || "eu-west-1";

const MAILGUN_OPTIONS = mailgunTransport({ auth: { api_key: MAILGUN_API_KEY, domain: MAILGUN_DOMAIN } });

const SES_OPTIONS = {
    SES: {
        ses: new SES({
            credentials: {
                accessKeyId: AWS_ACCESS_KEY_ID,
                secretAccessKey: AWS_SECRET_ACCESS_KEY
            },

            region
        }),
        aws: SES
    }
};

const TRANSPORT_OPTIONS = {
    ses: SES_OPTIONS,
    mailgun: MAILGUN_OPTIONS
};

let provider = process.env.EMAIL_PROVIDER || "mailgun";
if (!Object.keys(TRANSPORT_OPTIONS).includes(provider)) provider = "mailgun";

export const transporter = nodemailer.createTransport(TRANSPORT_OPTIONS[provider]);
