import { afterAll } from "@jest/globals";
import mongoose from "mongoose";
import { redis } from "config/redis";

export default async () => {
    afterAll(async () => {
        await new Promise((resolve) => {
            redis.quit(() => {
                resolve();
            });
        });
        await new Promise((resolve) => {
            setImmediate(resolve);
        });
        await mongoose.connection.close();
    });
};
