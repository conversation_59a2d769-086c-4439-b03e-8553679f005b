export const EMAIL = {
    APPOINTMENT_CANCELED: { key: "appointment_canceled", subject: "Appointment Canceled" },
    APPOINTMENT_REMINDER: { key: "appointment_reminder", subject: "Appointment Reminder" },
    APPOINTMENT_RESCHEDULE: { key: "appointment_reschedule", subject: "Appointment Rescheduled" },
    NEW_APPOINTMENT: { key: "new-appointment", subject: "Appointment Scheduled" },
    ASSIGN_TASK: { key: "assign_task", subject: "New Task Assigned" },
    CHANGE_PLAN: { key: "change_plan", subject: "CHANGE PLAN" },
    CHANGED_CARD: { key: "changed_card", subject: "CHANGED CARD" },
    CHECKOUT_LINK: { key: "checkout_link", subject: "CHECKOUT LINK" },
    DEACTIVATION_WARNING: { key: "deactivation_warning", subject: "DEACTIVATION WARNING" },
    DISCONNECTION_NOTIFICATION: {
        key: "disconnection_notification",
        subject: "DISCONNECTION NOTIFICATION"
    },
    DOCKET_UPDATE: { key: "docket_update", subject: "DOCKET UPDATE" },
    NEW_DEVICE: { key: "new_device", subject: "NEW DEVICE" },
    NEW_ORGANIZATION: { key: "new_organization", subject: "NEW ORGANIZATION" },
    PATIENT_CREDIT_TOPUP: { key: "patient_credit_topup", subject: "Patient Credit Subscription" },
    PATIENT_REFERRAL: { key: "patient_referral", subject: "PATIENT REFERRAL" },
    PATIENT_REGISTRATION: { key: "patient_registration", subject: "PATIENT REGISTRATION" },
    REFERRAL_NOTIFICATION: { key: "referral_notification", subject: "REFERRAL NOTIFICATION" },
    REFUND_REQUEST: { key: "refund_request", subject: "REFUND REQUEST" },
    RESET_PASSWORD: { key: "reset_password", subject: "Indigo: Reset Password Link" },
    STAFF_SUBSCRIPTION: { key: "staff_subscription", subject: "STAFF SUBSCRIPTION" },
    STOCK_UPDATE: { key: "stock_update", subject: "STOCK UPDATE" },
    SUBSCRIPTION_CANCELLED: { key: "subscription_cancelled", subject: "SUBSCRIPTION CANCELLED" },
    SUBSCRIPTION_REMINDER: { key: "subscription_reminder", subject: "SUBSCRIPTION REMINDER" },
    TRANSFER_AUTHORIZATION: { key: "transfer_authorization", subject: "TRANSFER AUTHORIZATION" },
    WALLET_TRANSFER_OTP: { key: "wallet_transfer_otp", subject: "Wallet Transfer Verification Required" }
};
