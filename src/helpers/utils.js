import bcrypt from "bcryptjs";
import cuid from "cuid";
import { addMinutes, addMonths, addYears } from "date-fns";
import { subscriptionIntervals, subscriptionPlans } from "helpers/constants";
import { capitalize } from "inflection";
import <PERSON><PERSON> from "joi";
import jwt from "jsonwebtoken";
import { chunk } from "lodash";
import moment from "moment";
import { isValidObjectId, model } from "mongoose";
import { authenticator } from "otplib";
import Papa from "papaparse";
import request from "request";
import { StringStream } from "scramjet";
import throwErrorWithCode from "utils/throw-error";

const { UTILS_SECRET, LICENSE_QA_MODE, NODE_ENV } = process.env;

authenticator.options = { step: 600 };
const { clinic, hospital, hospitalPlus } = subscriptionPlans;

export const generateHash = (password) => {
    const salt = bcrypt.genSaltSync();
    return bcrypt.hashSync(password, salt);
};

export const comparePassword = (hash, password) => bcrypt.compareSync(password, hash);

export const generateOTP = (secret) => {
    authenticator.options = { digits: 4 };
    let token = authenticator.generate(secret);
    token = token.padStart(4, "1");
    return token;
};

export const checkOTP = (token, secret) => authenticator.verify({ token, secret });

export const generateToken = (email) => {
    const token = jwt.sign({ email }, UTILS_SECRET, { expiresIn: "1h" });
    return token;
};

export const verifyToken = (token) => {
    return jwt.verify(token, UTILS_SECRET);
};

const refPrefix = {
    order: "od",
    transfer: "tx",
    recall: "rc",
    returns: "rt",
    consumables: "co",
    sales: "ss",
    "new-stock": "ns",
    bill: "inv",
    receipt: "rct",
    "stock-reconciliation": "sr",
    "organization-transaction": "org-tx"
};
export const generateRefNo = (type) => {
    return `${refPrefix[type]}-${cuid()}`;
};

export const getTimeSlots = (start, end, interval) => {
    const startTime = moment(start, "hh:mm a");
    const endTime = moment(end, "hh:mm a");

    if (endTime.isBefore(startTime)) {
        endTime.add(1, "day");
    }

    const timeSlots = [];

    while (startTime <= endTime) {
        timeSlots.push(moment(startTime).format("HH:mm"));
        startTime.add(interval, "minutes");
    }

    return chunk(timeSlots, 2);
};

/**
 * Validate column headers matches expected.
 * @param {object} row
 * @param {[String]} expectedHeaders
 */
export const validateCsvHeader = (row, expectedHeaders) => {
    const actualHeaders = Object.keys(row);
    const headerValid = expectedHeaders.every((expected) => actualHeaders.includes(expected));
    if (!headerValid) {
        throwErrorWithCode(`Invalid Column(s). Expected column headers: ${expectedHeaders.join(", ")}`);
    }
};

export const parseStringToNumber = (numbStr) => {
    /* eslint-disable */
    return typeof numbStr === "string" ? parseFloat(numbStr.replace(/[^\d\.]/g, "")) : numbStr;
};

export const computeTotalStaffPrice = (noOfStaff) => {
    let amountPerStaff;
    switch (true) {
        case noOfStaff <= parseInt(clinic.userLimit, 10):
            amountPerStaff = parseInt(clinic.pricePerUser, 10);
            break;
        case noOfStaff > parseInt(clinic.userLimit, 10) && noOfStaff <= parseInt(hospital.userLimit, 10):
            amountPerStaff = parseInt(hospital.pricePerUser, 10);
            break;
        case noOfStaff > parseInt(hospital.userLimit, 10) && noOfStaff < parseInt(hospitalPlus.userLimit, 10):
            amountPerStaff = parseInt(hospitalPlus.pricePerUser, 10);
            break;
        default:
            amountPerStaff = hospitalPlus.pricePerUser;
    }
    return { total: amountPerStaff * noOfStaff, amountPerStaff };
};

export const computeTotalPatientsPrice = (staffCount, patientsCount) => {
    let amountPerPatient = parseInt(hospital.pricePerPatient, 10);
    if (staffCount < parseInt(clinic.userLimit, 10)) {
        amountPerPatient = parseInt(clinic.pricePerPatient, 10);
    }
    return { total: amountPerPatient * patientsCount, amountPerPatient };
};

export const calcNextPaymentDate = (interval, nextPaymentDate) => {
    if (interval === subscriptionIntervals.monthly) {
        return addMonths(new Date(nextPaymentDate), 1);
    }
    if (interval === subscriptionIntervals.yearly) {
        return addYears(new Date(nextPaymentDate), 1);
    }
    return new Date(nextPaymentDate);
};

export const nairaToKobo = (amount) => amount * 100;

/**
 * Sort array of objects by attribute
 * @param attribute
 * @returns {(function(*, *): (number))|*}
 */
export const compareFn = (attribute) => (prev, next) => {
    if (prev[attribute] < next[attribute]) return -1;
    if (prev[attribute] > next[attribute]) return 1;
    return 0;
};

export const valueIsNonEmpty = (value) => {
    if (value === null || value === undefined) return false;
    if (Array.isArray(value) && value.length > 0) return true;
    if (typeof value === "object" && Object.keys(value).length > 0) return true;
    return Boolean(value);
};

/**
 *
 * @param {Object} obj
 * @param {Array<String>} properties
 * @returns {Object}
 */
export const removePropertiesFromObject = (obj, properties) => {
    return properties.reduce((fields, key) => {
        delete fields[key];
        return fields;
    }, obj);
};

/**
 * Compute the difference between two objects
 * @param {Object} originalObject
 * @param {Object} changedObject
 * @returns {Object}
 */
export const computeDiff = (originalObject = {}, changedObject) => {
    return Object.keys(changedObject).reduce((diff, field) => {
        const oldValue = originalObject[field];
        const newValue = changedObject[field];
        if (["boolean", "string", "date", "number"].includes(typeof newValue) && oldValue !== newValue) {
            diff[field] = { old: oldValue, new: newValue };
        }
        if (typeof newValue === "object" && !Array.isArray(newValue)) diff[field] = computeDiff(oldValue, newValue);
        if (Array.isArray(newValue)) {
            diff[field] = newValue.map((item, index) => {
                if (typeof item === "object" && !Array.isArray(item)) return computeDiff(oldValue[index], item);
                return item;
            });
        }
        return diff;
    }, {});
};

/**
 *
 * @param {Array<String|Number>} arr
 * @param {Array<String|Number>} elements
 * @returns {Array<String|Number>}
 */
export const removeElementsFromArray = (arr, elements) => arr.filter((element) => !elements.includes(element));

export const escapeSpecialCharacters = (text) => {
    return text.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&").trim();
};

export const generateFixedRandomNumber = (length) => {
    return Math.random().toString().substr(2, length);
};

export const addHoursToDate = (dateObj, hours) => {
    return new Date(dateObj.getTime() + hours * 60 * 60 * 1000);
};

export const removeHoursFromDate = (dateObj, hours) => {
    return new Date(dateObj.getTime() - hours * 60 * 60 * 1000);
};

/**
 * Checks if the current environment matches one of the specified environments
 * @param {String} envs - pipe separated list of environments
 * @returns {Boolean}
 */
export const isEnv = (envs) => {
    const environments = envs.split("|").map((environment) => environment.trim());
    return environments.includes(NODE_ENV);
};

export const composeFiles = (array) => {
    const files = [];

    array?.filter((file) => {
        const doc = {
            name: file?.name,
            binaryString: file?.binaryString
        };

        files.push(doc);
    });

    return files;
};
/**
 * Function to split an array of objects into existing and new entries based on the presence of a field
 * @param {Array<object>} arr
 * @param {string} field
 * @returns {{existing: Array, new: Array}}
 */
export const splitIntoExistingAndNew = (arr, field) => {
    return arr.reduce(
        (acc, item) => {
            if (item[field] === "none") {
                acc.new.push(item);
            } else {
                acc.existing.push(item);
            }
            return acc;
        },
        { existing: [], new: [] }
    );
};
/**
 * GIven an array of objects, this function groups the values of specified keys into an object of array per key
 * @param {Array<object>} arr
 * @param {Array<string>} keys
 * @returns {{[key]: Array<string>}}
 */
export const groupValuesByKeys = (arr, keys) => {
    const result = {};
    for (const obj of arr) {
        keys.forEach((key) => {
            const value = obj[key] ? [obj[key]] : [];
            result[key] = result[key] ? [...new Set(result[key].concat(value))] : value;
        });
    }
    return result;
};

/**
 * Given a value and a total, this function calculates the percentage
 * @param {number} value
 * @param {number} total
 * @returns {number}
 */
export const calculatePercentage = (value, total) => {
    let percent;
    if (value === 0 && total === 0) {
        percent = 0;
    } else {
        percent = ((Number(value) / Number(total)) * 100).toFixed(2);
    }
    return percent;
};

export const _joinArr = (arr = [], ellipsis = false) => {
    if (arr.length < 2) {
        return arr.join(" and ");
    } else if (ellipsis) {
        const array = arr.slice(0, -1);
        return `${array.join(", ")}, ${arr[arr.length - 1]}...`;
    }
    const array = arr.slice(0, -1);
    return `${array.join(", ")} and ${arr[arr.length - 1]}`;
};

export const _arrayObjectToString = (arr, label) => {
    let temp = [];
    if (arr?.length) {
        temp = arr.map((arrayObject) => arrayObject[label]);
    }
    return _joinArr([...new Set(temp)]);
};

export const _arrayObjectToArrayString = (arr, field, otherField) => {
    let temp = [];
    if (arr?.length) {
        temp = arr.flatMap((arrayObject) => {
            const value = arrayObject[field] && otherField ? arrayObject[field][otherField] : arrayObject[field];
            return [null, undefined].includes(value) ? "N/A" : value;
        });
    }
    return temp;
};

export const removeDuplicatesFromArray = (arr, key) => {
    return Object.values(
        arr.reduce((acc, obj) => {
            const data = obj[key];
            if (!acc[data]) {
                acc[data] = obj;
            }
            return acc;
        }, {})
    );
};

export const composeSpecialtyWrites = (record) => {
    const specialties = {
        "General Practice": "generalPractice",
        Ophthalmology: "ophthalmology"
    };
    return record.map(({ _id, specialty }) => ({
        updateOne: {
            filter: { _id },
            update: {
                $set: {
                    specialty: specialties[specialty]
                }
            }
        }
    }));
};

export const convertSpecialties = (specialtiesArray) => {
    const specialties = {
        "General Practice": "generalPractice",
        Ophthalmology: "ophthalmology"
    };
    return specialtiesArray.map((specialty) => specialties[specialty]);
};

export const composeSpecialtiesWrites = (record) => {
    return record.map(({ _id, specialties }) => ({
        updateOne: {
            filter: { _id },
            update: {
                $set: {
                    specialties: convertSpecialties(specialties)
                }
            }
        }
    }));
};
/**
 *
 * @param {String} phoneNumber
 * @returns {String} masked phone number
 */
export const maskPhoneNumber = (phoneNumber) => {
    const head = phoneNumber.slice(0, -4);
    const last4Nums = phoneNumber.slice(-4);
    return `${head.replace(/./g, "*")}${last4Nums}`;
};

/**
 * Checks if an object has a key
 * @param {object} obj
 * @param {String} key
 * @returns boolean
 */
export const objectHasKey = (obj, key) => Object.hasOwn(obj, key);

export const convertKoboToNaira = (price) => {
    return price / 100;
};
/**
 * Creates a regular expression for exact string matching.
 * @param {string} str
 * @returns {RegExp}
 */
export const createExactMatchRegex = (str) => new RegExp(`^${escapeSpecialCharacters(str)}$`, "i");

export const paginationSchema = {
    page: Joi.number().min(1).default(1),
    limit: Joi.number().max(20).min(1).default(20),
    exportLimit: Joi.number()
};

/**
 * Transforms an array of strings into an array of exact match regular expressions.
 *
 * This function takes an array of strings and converts each non-empty string
 * into a regular expression that matches that string exactly. Non-string
 * values are left as-is. Empty or falsy values are filtered out.
 * @param {Array<string>} arr
 * @returns {Array<RegExp>}
 */
export const mapToExactMatchRegex = (arr) => {
    return arr.filter(Boolean).map((value) => (typeof value === "string" ? createExactMatchRegex(value) : value));
};

export const formatCurrency = (currency, amount) => {
    const formatter = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: currency.toUpperCase()
    });

    return formatter.format(Number(amount));
};

/**
 * Convert object to array of name value objects
 * @param {*} obj
 * @param {*} options { capitalize: bool }
 */
export const _objectToNameValuePair = (obj, options = {}) => {
    return Object.keys(obj).map((key) => ({
        name: options?.capitalize ? capitalize(obj[key]) : obj[key],
        value: obj[key]
    }));
};

/**
 * Validate if all item in a obj have a valid value
 * @param {*} obj
 * @param {*} options { capitalize: bool }
 */
export const allObjectKeysHaveNoValues = (obj) => {
    return Object.keys(obj).every((key) => {
        let value = obj[key];
        if (typeof value === "string") value = value.trim();
        return [undefined, ""].includes(value);
    });
};

export const nameValidatorSchema = {
    givenName: Joi.string().when("_id", { not: Joi.exist(), then: Joi.required() }),
    lastName: Joi.string().when("_id", { not: Joi.exist(), then: Joi.required() })
};

export const toDateValidationSchema = (fromDateField = "fromDate") => {
    return Joi.date().when(fromDateField, {
        is: Joi.exist(),
        then: Joi.date().greater(Joi.ref(fromDateField)),
        otherwise: Joi.date()
    });
};

export const composeDateFilter = (fromDate, toDate) => {
    return { ...(fromDate && { $gte: fromDate }), ...(toDate && { $lte: toDate }) };
};

export const signatureModelSchema = {
    name: { type: "String", required: true },
    attachment: { type: "String", required: true },
    date: { type: "Date", required: true }
};
/**
 * This is to get any one record from the db
 * @param {object} source
 * @param {string} source._id
 * @param {string} source.name -> Name of the db model
 * @param {string} source.parentOrganizationId
 * @param {*} options -> Mongoose options e.g. populate, lean etc
 * @returns
 */
export const getDocument = async (source, options = {}) => {
    const { _id, name: modelName, parentOrganizationId } = source;
    return model(modelName).findOne({ _id, parentOrganizationId }, {}, { lean: true, ...options });
};

export const sortObject = (obj) => {
    return Object.keys(obj)
        .sort()
        .reduce((acc, key) => {
            acc[key] = obj[key];
            return acc;
        }, {});
};

/**
 * dowload, extract, transform and load csv file
 * @param {*} fileLocation
 * @param {object} options
 * @param {boolean} options.fileLocationIsDataStream
 * @returns
 */
export const downloadCSVFileETL = (fileLocation, options = {}) => {
    return new Promise((resolve) => {
        const dataStream = !options.fileLocationIsDataStream
            ? request.get(fileLocation).pipe(new StringStream({ encoding: "utf-8" }))
            : fileLocation;

        Papa.parse(dataStream, {
            header: true,
            skipEmptyLines: true,
            transformHeader: (header) => header.replace(/['"]+/g, "").trim(),
            complete: function (results) {
                resolve(results);
            }
        });
    });
};

export const splitArrayInTwo = ({ key, firstBucket, secondBucket, schema = [] }) => {
    return schema.reduce(
        (acc, curr) => {
            if (curr[key]) acc[firstBucket].push(curr);
            else acc[secondBucket].push(curr);
            return acc;
        },
        { [firstBucket]: [], [secondBucket]: [] }
    );
};

/**
 * This function converts stringified booleans to actual boolean values.
 * @param {string} str - The stringified booleans
 * @returns {boolean}
 */
export const parseBoolean = (str) => {
    return str !== "false";
};

export const getRandomAgendaInterval = () => {
    const minInterval = 5 * 1000; // 5 seconds
    const maxInterval = 5 * 60 * 1000; // 5 minutes
    const randomInterval = Math.floor(Math.random() * (maxInterval - minInterval + 1)) + minInterval;
    const seconds = Math.floor(randomInterval / 1000);
    return `in ${seconds} seconds`;
};

/**
 *
 * @param {object} staff
 * @param {string} pin
 */
export const validatePin = (staff, pin) => {
    if (!staff.pin) throwErrorWithCode("PIN not added to profile.");
    const pinMatch = comparePassword(staff.pin, pin);
    if (!pinMatch) throwErrorWithCode("The supplied PIN is incorrect", 403);
};

/**
 * Delete properties from an object
 * @param {object} obj
 * @param {[String]} properties
 * @returns {object}
 */
export const deleteFromObject = (obj, properties) => {
    return Object.keys(obj).reduce((newObj, property) => {
        if (properties.includes(property)) delete obj[property];
        return newObj;
    }, obj);
};

export const isProductionOrNonQAMode = isEnv("production") || !LICENSE_QA_MODE;
/**
 * Given the subscription interval, calculate expiry date
 * @param {string} interval
 * @returns {date}
 */
export const calculateLicenseExpiryDate = (interval = "monthly") => {
    let expiryDate = isProductionOrNonQAMode ? addMonths(new Date(), 1) : addMinutes(new Date(), 30);
    if (interval === "yearly") {
        expiryDate = isProductionOrNonQAMode ? addYears(new Date(), 1) : addMinutes(new Date(), 45);
    }
    return expiryDate;
};

/**
 * Generates a hashmap from an array of objects, using the specified attribute as the key.
 *
 * @param {Array<Object>} arr - The array of objects to convert into a hashmap.
 * Each object should contain the attribute used as the key.
 * @param {string} attribute - The name of the attribute in each object to use as the key for the hashmap.
 *
 * @returns {Object} - The resulting hashmap where the keys are the values of the specified attribute,
 * and the values are the corresponding objects from the input array.
 *
 * @throws {Error} - Throws an error if the first argument is not an array,
 * or if the attribute is not a string, or if the attribute doesn't exist in an object.
 */
export const generateHashMap = (arr, attribute) => {
    if (!Array.isArray(arr)) throw new Error("First argument must be an array");
    if (typeof attribute !== "string") throw new Error("Attribute must be a string");
    return arr.reduce((hashMap, next) => {
        if (next[attribute] === undefined) {
            throw new Error(`Attribute '${attribute}' not found in object`);
        }
        const key = next[attribute];
        hashMap[key] = next;
        return hashMap;
    }, {});
};

export const categorizeBy = (iterable, callbackFn) => {
    if (!iterable || typeof iterable[Symbol.iterator] !== "function") {
        throw new TypeError("First argument must be an iterable");
    }
    if (typeof callbackFn !== "function") throw new TypeError("Second argument must be a function");
    const resultMap = new Map();
    iterable.forEach((item, index) => {
        const key = callbackFn(item, index);
        if (!resultMap.has(key)) resultMap.set(key, []);
        resultMap.get(key).push(item);
    });
    return Object.fromEntries(resultMap);
};

/**
 * Creates a regular expression object for partial string matching in MongoDB queries.
 * @param {string} value - The search string to match partially
 * @returns {Object} MongoDB query object with case-insensitive regex
 * @requires escapeSpecialCharacters - Function to escape regex special characters
 */
export const createPartialMatchRegex = (value) => {
    return { $regex: escapeSpecialCharacters(value), $options: "i" };
};

export const objectId = () =>
    Joi.string().custom((value, helpers) => {
        if (!isValidObjectId(value)) return helpers.error("any.invalid");
        return value;
    }, "ObjectId Validation");

/**
 * Checks if a value is a non-array object
 *
 * @param {*} element - The value to check
 * @returns {boolean} True if the value is an object but not an array
 */
export const isObject = (element) => typeof element === "object" && !Array.isArray(element);

/**
 * Checks if a value is an array
 *
 * @param {*} element - The value to check
 * @returns {boolean} True if the value is an array
 */
export const isArray = (element) => Array.isArray(element);
