import Joi from "joi";
import { Schema } from "mongoose";

export const modules = [
    "frontdesk",
    "billing",
    "pharmacy",
    "inventory",
    "in-patient",
    "out-patient",
    "laboratory",
    "report"
];
export const superPermissions = [
    { title: "super-create", display_title: "Can add/create any record" },
    { title: "super-view", display_title: "Can view any record" },
    { title: "super-edit", display_title: "Can edit any record" },
    { title: "super-delete", display_title: "Can delete any record" }
];
export const reasonsForRecall = ["defect", "adulteration", "contamination", "labelling-error", "ban", "expiration"];

export const appointmentStateConst = {
    VITALS: "vitals",
    CONSULTING: "consulting",
    CONSULTANT: "consultant",
    OUTPATIENTPROCEDURE: "outpatient procedure",
    OUTPATIENTSURGERY: "outpatient surgery",
    CONSULTANTCHECKOUT: "consultant checkout"
};
export const hasAppointmentEnded = {
    TRUE: true,
    FALSE: false
};
export const isCheckedIn = {
    TRUE: true,
    FALSE: false
};

export const isCheckedOut = {
    TRUE: true,
    FALSE: false
};

export const addressSchema = new Schema(
    {
        exact: { type: "String", required: true },
        googleAddress: { type: "String" },
        city: { type: "String" },
        state: { type: "String" },
        country: { type: "String" }
    },
    { _id: false }
);

export const addressValidatorSchema = {
    exact: Joi.string().required(),
    googleAddress: Joi.string(),
    city: Joi.string().required(),
    state: Joi.string().required(),
    country: Joi.string().required()
};

export const idNameSchema = new Schema({
    _id: { type: "String" },
    name: { type: "String" }
});

export const idNameRequiredSchema = new Schema({
    _id: { type: "String", required: true },
    name: { type: "String", required: true }
});

export const fileValidatorSchema = Joi.object({
    name: Joi.string().required().trim(),
    type: Joi.string().required().trim(),
    binaryString: Joi.string().required().trim()
});
export const fileValidatorRequiredSchema = fileValidatorSchema.required();

export const paymentMethods = ["cash", "pos", "refund", "transfer", "cheque", "direct-lodgement", "online", "wallet"];
export const paymentTypes = {
    EMRSubscription: "EMR Subscription",
    patientsLicencesSubscription: "Patients Licences Subscription",
    staffLicensesPurchase: "Staff Licenses Purchase",
    staffSubscription: "Staff Subscription",
    telemed: "TeleMed",
    changePaymentMethod: "Change Payment Method",
    creditTopUp: "Credit top-up",
    cardRenewalAndSubscriptionPayment: "Card Renewal And Subscription"
};
export const SubscriptionTypes = {
    EMRUsers: "EMR Users",
    patientsLicences: "Patients Licences"
};

export const BILL_POPULATION_PATHS = [
    { path: "patient", select: "name mrn sponsors gender wallet owing, admission" },
    { path: "claims" },
    { path: "branchId", select: "name" }
];

export const receiptTransaction = [
    { path: "from", select: "mrn name wallet sponsors gender owing" },
    { path: "to", select: "name mrn" }
];

export const subscriptionIntervals = {
    monthly: "monthly",
    yearly: "yearly"
};

export const paymentPlans = {
    hospital: "hospital",
    clinic: "clinic",
    hospitalPlus: "hospital plus",
    national: "national"
};
export const subscriptionPlans = {
    clinic: {
        title: "clinic",
        userLimit: 15,
        pricePerUser: 0
    },
    hospital: {
        title: "hospital",
        userLimit: 50,
        pricePerUser: 6500
    },
    hospitalPlus: {
        title: "hospital plus",
        userLimit: 100,
        pricePerUser: 6200
    },
    national: {
        title: "national"
    }
};
export const perPatientPrice = {
    new: 400,
    existing: 250
};

export const SPECIALTIES = {
    Ophthalmology: "ophthalmology",
    General: "generalPractice",
    "Obstetrics and Gynaecology": "obgyn",
    Endocrinology: "endocrinology",
    Cardiology: "cardiology",
    Paediatrician: "paediatrician",
    Physiotherapy: "physiotherapy",
    Orthopaedic: "orthopaedic",
    Dentist: "dentist",
    Urology: "urology",
    Neurology: "neurology",
    Oncology: "oncology",
    Nephrology: "nephrology",
    "Cardio Thoracic": "cardiothoracic",
    "General Surgeries": "generalSurgeries",
    Rheumatology: "rheumatology",
    "General Family Physician": "generalFamilyPhysician"
};

export const useSpecialtyQuery = (specialty) => {
    return { specialty: specialty };
};

export const specialtySchema = () => {
    return { specialty: { type: "String", index: true, required: true } };
};

export const specialtiesSchema = () => {
    return { specialties: [{ type: "String", index: true, required: true }] };
};

export const sourceRequest = () => {
    return {
        source: Joi.object({
            _id: Joi.string().required(),
            name: Joi.string().valid("Appointment", "Admission").required()
        }).required()
    };
};

export const specialtyRequest = () => {
    return {
        specialty: Joi.string()
            .valid(...Object.values(SPECIALTIES))
            .required()
    };
};

export const idNameRequestRequiredSchema = () => {
    return Joi.object({
        _id: Joi.string().required(),
        name: Joi.string().required()
    }).required();
};

export const idNameRequestSchema = () => {
    return Joi.object({
        _id: Joi.string(),
        name: Joi.string()
    });
};

export const arrayOfIdNameRequestSchema = Joi.array().items(idNameRequestSchema());

export const specialtiesRequest = () => {
    return {
        specialties: Joi.array()
            .items(
                Joi.string()
                    .valid(...Object.values(SPECIALTIES))
                    .required()
            )
            .unique()
    };
};

export const specialtiesRequiredRequest = () => {
    return {
        specialties: Joi.array()
            .items(Joi.string().valid(...Object.values(SPECIALTIES)))
            .unique()
            .required()
    };
};

export const stockItemEvents = [
    "added-stock",
    "archived-stock",
    "restored-archived-stock",
    "received-stock-from-patient",
    "received-stock-from-branch",
    "received-stock-from-staff",
    "dispensed-consumables",
    "dispensed-stocks",
    "aggregated-stock-balances"
];

export const socketRooms = {
    VITALS: "VITALS",
    PHARMACY: "PHARMACY",
    ACCOUNTS: "ACCOUNTS",
    FRONTDESK: "FRONTDESK",
    CONSULTANTS: "CONSULTANTS",
    OPTOMETRIST: "OPTOMETRIST",
    RESIDENTDOCTOR: "RESIDENTDOCTOR"
};

export const roleTags = {
    VITALS: "vitals",
    OPHTHALMOLOGIST: "ophthalmologist",
    CONSULTANT: "consultant",
    OPTOMETRIST: "optometrist",
    RESIDENTDOCTOR: "resident doctor"
};

export const JOB_EXECUTION_TIME = "in 5 seconds";

export const backDateSchema = new Schema({
    _id: 0,
    by: idNameRequiredSchema,
    when: { type: "Date", required: true }
});

export const variantSchema = {
    _id: { type: "String", required: true },
    title: { type: "String", required: true },
    sku: { type: "String" }
};

export const SMS_PURPOSE_LIST = {
    BIRTHDAY: "birthday",
    REGISTRATION: "registration",
    APPOINTMENT_VISIT: "appointment visit",
    APPOINTMENT_COMPLETION: "appointment completion",
    FOLLOW_UP_VISIT: "follow up visit",
    RECOMMENDED_SURGERY: "recommended surgery",
    RECOMMENDED_PROCEDURE: "recommended procedure",
    RECOMMENDED_ADMISSION: "recommended admission",
    DRUG_REFILL: "drug refill",
    PRESCRIPTION: "prescription",
    MED_DOCKET: "med docket",
    RECALL: "recall",
    PAYMENT: "payment",
    PATIENT_CREDIT: "patient credit",
    FESTIVAL: "festival",
    DISCHARGE: "discharge",
    LAB_RESULT: "lab result"
};

export const treatmentPlanSchema = {
    casenoteId: Joi.string().trim().when("treatmentPlan", { is: true, then: Joi.required() }),
    treatmentPlan: Joi.boolean().default(false)
};

export const MEDICAL_RECORD_SOURCE = {
    ADMISSION: "Admission",
    APPOINTMENT: "Appointment"
};

export const medicalRecordSourceSchema = Joi.object({
    _id: Joi.string().trim().required(),
    name: Joi.string()
        .required()
        .valid(...Object.values(MEDICAL_RECORD_SOURCE))
});

export const smsCostPerPage = 400;

export const ITEM_VARIANTS = [
    "size",
    "length",
    "gender",
    "class",
    "adult or kid",
    "type",
    "frame",
    "material",
    "style",
    "bridge",
    "color"
];

export const MEDICAL_RECORD_MODELS = [
    "WardRound",
    "NursingNote",
    "RxPrescription",
    "Procedure",
    "ProgressNote",
    "CaseNotes",
    "MedicalReferral",
    "Vital",
    "Investigation",
    "Assessment",
    "ContinuousAssessment",
    "SoapNote",
    "Observation",
    "FluidBalance",
    "IVFluid",
    "ConsentForm",
    "Surgery",
    "FormEntry"
];
export const EXCEL_MIME_TYPES = [
    "text/csv",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
];

export const CLINIC_SOURCE = {
    ADMISSION: "Admission",
    APPOINTMENT: "Appointment"
};

export const CLINIC_SOURCE_SCHEMA = Joi.object({
    _id: Joi.string().required(),
    name: Joi.string()
        .required()
        .valid(...Object.values(CLINIC_SOURCE))
});

export const MEDICAL_STANDARD = {
    CUSTOM: "custom",
    ICD10: "icd10"
};
