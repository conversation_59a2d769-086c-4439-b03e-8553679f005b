import Server from "socket.io";

const io = Server();

const Socket = {
    emit: function (event, data) {
        io.sockets.emit(event, data);
    },
    to: function (room, event, data) {
        io.sockets.to(room).emit(event, data);
    },

    send: function (rooms, event, data) {
        io.sockets.to(rooms).emit(event, data);
    }
};

io.on("connection", function (socket) {
    console.log(`A user connected ${socket.id}`);

    const room = socket.handshake.query.channelName;
    socket.join(room);
});

export { Socket, io };
