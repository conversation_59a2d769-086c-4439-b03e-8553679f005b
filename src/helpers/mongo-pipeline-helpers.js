import { deleteFromObject, escapeSpecialCharacters } from "./utils";

/**
 * Given an array field, this function joins the array values into a single string.
 * @param {string} arrField
 * @param {string} emptyStateText
 * @returns {object}
 */
export const pipelineJoinArray = (arrField, emptyStateText = "N/A") => {
    arrField = arrField.startsWith("$") ? arrField : `$${arrField}`;
    return {
        $cond: [
            { $eq: [{ $size: { $ifNull: [arrField, []] } }, 0] },
            emptyStateText,
            {
                $reduce: {
                    input: arrField,
                    initialValue: "",
                    in: { $cond: [{ $eq: ["$$value", ""] }, "$$this", { $concat: ["$$value", ", ", "$$this"] }] }
                }
            }
        ]
    };
};
/**
 * Given a date field, this function formats the date to the specified format.
 * @param {string} dateField
 * @param {object} options
 * @param {string} options.format
 * @param {string} options.timezone
 * @returns {object}
 */
export const pipelineFormatDate = (dateField, options = {}) => {
    options = { format: "%d-%m-%Y", timezone: "GMT", ...options };
    const { format, timezone } = options;
    return { $ifNull: [{ $dateToString: { format, date: `$${dateField}`, timezone } }, "NA"] };
};

/**
 * Reusable lookup pipeline stage.
 * @param {string} field
 * @returns {object}
 */
export const pipelineLookup = ({ from, localField, foreignField, as, variable, select }) => {
    return {
        $lookup: {
            let: { [variable]: { $toObjectId: `$${localField}` } },
            from,
            pipeline: [{ $match: { $expr: { $eq: [`$${foreignField}`, `$$${variable}`] } } }, { $project: select }],
            as
        }
    };
};

/**
 * Reusable lookup pipeline stage for organization.
 * @param {String} localField
 * @param {object} fieldsToProject
 * @returns
 */
export const lookupOrg = (localField, fieldsToProject = { name: 1, "config.timeZone": 1 }, as = "branch") => {
    return pipelineLookup({
        from: "organizations",
        localField,
        foreignField: "_id",
        as,
        variable: "branchId",
        select: fieldsToProject
    });
};

const PATIENT_DEFAULT_SELECT = { name: 1, dateOfBirth: 1, mrn: 1, address: 1, gender: 1, phoneNumber: 1, email: 1 };

/**
 * Reusable lookup pipeline stage for patients.
 * @param {string} localField - Field to match with the patient _id.
 * @param {object} [fieldsToProject] - Fields to project from the lookup stage.
 * Default is { name, dateOfBirth, mrn, address, gender, phoneNumber, email }.
 * @returns {object}
 */
export const lookupPatient = (localField, fieldsToProject) => {
    fieldsToProject = { ...PATIENT_DEFAULT_SELECT, ...fieldsToProject };
    return pipelineLookup({
        from: "patients",
        localField,
        foreignField: "_id",
        as: "patient",
        variable: "patientId",
        select: fieldsToProject
    });
};

/**
 * Reusable lookup pipeline stage to compute age of a field in years.
 * @param {String} startDate
 * @returns
 */
export const calculateAge = (startDate) => {
    return {
        $ifNull: [{ $dateDiff: { startDate, endDate: new Date(), unit: "year" } }, "NA"]
    };
};

export const generatePatientFilter = (baseFilter) => {
    const patientFilterAttributes = Object.keys(baseFilter).filter((attribute) => attribute.startsWith("patient."));
    const patientFilter = patientFilterAttributes.reduce((filter, attribute) => {
        filter[attribute] = baseFilter[attribute];
        if (attribute === "patient.allergies") {
            filter["patient.allergies"] = {
                $regex: escapeSpecialCharacters(filter["patient.allergies"]),
                $options: "i"
            };
        }
        return filter;
    }, {});
    deleteFromObject(baseFilter, patientFilterAttributes);
    return patientFilter;
};
