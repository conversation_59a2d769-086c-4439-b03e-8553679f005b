import Log from "config/logger";
import RedisUtils from "utils/redis";
import { isEnv } from "./utils";

const { NODE_ENV } = process.env;

export const RESPONSE_STATUS = {
    PROCESSING: "processing",
    FAILED: "failed",
    SUCCESS: "success"
};

export const IDEMPOTENCY_KEY = "Indigo-Idempotency-Key";

const getUserDetails = (user) => {
    const { _id: accountId, username, type, device } = user;
    const { _id, name, parentOrganizationId, permissions, sections, specialties } = user.currentLocation;
    const location = { _id, name, parentOrganizationId };
    return { accountId, username, type, permissions, location, device, sections, specialties };
};

const updateIdempotencyStatus = async (req, status) => {
    const idempotencyKey = req.get(IDEMPOTENCY_KEY);
    await RedisUtils.set(idempotencyKey, { status }, { EX: status === RESPONSE_STATUS.SUCCESS ? 86400 : 300 });
};

const logRequest = async ({ req, status, errorMessage, stack, code: statusCode }) => {
    await updateIdempotencyStatus(req, status);
    const endpoint = req.originalUrl;
    const body = { date: new Date(), method: req.method, endpoint, statusCode, status, env: NODE_ENV };
    if (req.query) body.queryParams = req.query;
    if (req.body && !isEnv("production")) body.payload = req.body;
    if (req.user?.currentLocation) body.user = getUserDetails(req.user);
    if (status === RESPONSE_STATUS.FAILED) Log.error(endpoint, { ...body, errorMessage, stack });
};

export const formatDuplicateKeyError = (errorMessage) => {
    const duplicateKeyRegex = /index: (\w+) dup key: \{ (\w+): "(.*?)"(, (\w+): "(.*?)")? \}/;
    const match = errorMessage.match(duplicateKeyRegex);
    if (match) {
        const attributeName = match[2];
        const attributeValue = match[3];
        return `An entry with ${attributeName} "${attributeValue}" already exists.`;
    }
    return "A duplicate entry already exists in the database.";
};

export const error = (res, code, err, data) => {
    const message = err.message || err;
    const isDuplicateKeyError = message.includes("E11000");
    if (isDuplicateKeyError) code = 409;
    code = err.statusCode || code;
    logRequest({ req: res.req, errorMessage: message, status: RESPONSE_STATUS.FAILED, stack: err.stack, code });
    return res.status(code).send({
        status: false,
        message: isDuplicateKeyError ? formatDuplicateKeyError(message) : message,
        data
    });
};

export const success = async (res, code, data) => {
    return res.status(code).send({ status: "true", message: "Successful", data });
};
