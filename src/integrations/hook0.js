/* eslint-disable camelcase */
import axios from "axios";
import throwErrorWithCode from "utils/throw-error";
import { sendIncidentReport } from "utils/uptime";

const { HOOK0_API_KEY, HOOK0_APP_ID: application_id, HOOK0_URL: BASE_URL } = process.env.NODE_ENV;

const Authorization = { Authorization: `Bearer ${HOOK0_API_KEY}` };

/**
 * @param {object} payload
 * @param {string} payload.url
 * @param {[string]} payload.eventTypes
 * @param {object} payload.headers
 * @param {object} payload.webhook
 * @param {object} payload.location
 * @returns {Promise<string>} subscriptionId
 */
const createSubscription = async (payload) => {
    try {
        const { url, headers, eventTypes: event_types, webhook, location } = payload;
        const response = await axios.post(
            `${BASE_URL}/subscriptions`,
            {
                application_id,
                target: { headers: headers || {}, method: "POST", type: "http", url },
                event_types,
                is_enabled: true,
                label_key: "location",
                label_value: location._id,
                description: `${webhook.name} subscription by ${location.name}`
            },
            { headers: { Authorization } }
        );
        return response.data.subscription_id;
    } catch (err) {
        await sendIncidentReport("createSubscription", JSON.stringify(err));
        throwErrorWithCode("Issues setting integrations. Try again later", 500);
        return null;
    }
};
const sendEvent = async (eventType, payload) => {
    try {
        await axios.post(
            `${BASE_URL}/event`,
            {
                event_id: payload.event.id,
                event_type: eventType,
                application_id,
                payload: JSON.stringify(payload, null),
                payload_content_type: "application/json",
                occurred_at: new Date(),
                labels: { all: "yes" }
            },
            { headers: { Authorization } }
        );
    } catch (err) {
        await sendIncidentReport("sendWebhookEvent", JSON.stringify(err));
        throwErrorWithCode("Problem sending webhook event", 500);
    }
};

const updateSubscription = async (subscriptionId, payload) => {
    try {
        const { url, headers, eventTypes: event_types, enabled: is_enabled } = payload;
        const response = await axios.put(
            `${BASE_URL}/subscriptions/${subscriptionId}`,
            {
                application_id,
                target: { headers: headers || {}, method: "POST", type: "http", url },
                event_types,
                is_enabled,
                label_key: "location",
                label_value: payload.location
            },
            { headers: { Authorization } }
        );
        return response.data.subscription_id;
    } catch (err) {
        await sendIncidentReport("updateSubscription", JSON.stringify(err));
        throwErrorWithCode("Issues updating integrations. Try again later", 500);
        return null;
    }
};

const Hook0Service = { createSubscription, sendEvent, updateSubscription };
export default Hook0Service;
