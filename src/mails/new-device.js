/* eslint-disable max-len */
const NewDevice = ({ date, name, ipAddress, parentHospital, operatingSystem, browser, platform }) => {
    return `
    <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Indigo Email Template</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preload"
        href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.woff"
        as="font" type="font/woff" crossorigin />
    <link rel="preload"
        href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.woff" as="font"
        type="font/woff" crossorigin />
    <link rel="preload"
        href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.woff"
        as="font" type="font/woff" crossorigin />
    <link rel="preload"
        href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.woff" as="font"
        type="font/woff" crossorigin />

    <style type="text/css">
        @font-face {
            font-family: "Circular-Std";
            font-weight: 400;
            font-display: swap;
            src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.eot"),
                url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.woff") format("woff"),
                url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.ttf") format("truetype"),
                url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.svg#webfont") format("svg");
        }

        @font-face {
            font-family: "Circular-Std";
            font-weight: 500;
            font-display: swap;
            src:
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.eot"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.woff") format("woff"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.ttf") format("truetype"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.svg#webfont") format("svg");
        }

        @font-face {
            font-family: "Circular-Std";
            font-weight: 700;
            font-style: normal;
            font-display: swap;
            src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.eot"),
                url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.woff") format("woff"),
                url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.ttf") format("truetype"),
                url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.svg#webfont") format("svg");
        }

        @font-face {
            font-family: "My Sunshine";
            font-weight: normal;
            font-style: normal;
            font-display: swap;
            src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.eot"),
                url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.woff") format("woff"),
                url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.ttf") format("truetype"),
                url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.svg#webfont") format("svg");
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
                "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
                "Helvetica Neue", sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        h1,
        h2,
        h3,
        h4 {
            font-family: "Circular-Std", sans-serif;
            font-weight: 500;
            font-size: 24px;
            color: #000000;
            margin-top: 0;
            letter-spacing: 1px;
        }

        p,
        ol,
        li,
        ul {
            font-family: "Circular-Std", sans-serif;
            font-weight: 400;
            font-size: 16px;
            color: #97a3b6;
            line-height: 20px;
            letter-spacing: 0.5px;
            margin-top: 0;
        }

        table {
            text-align: left;
        }

        .table__col {
            padding: 18px 0;
            border-bottom: 1px solid #e1e4e7;
        }
    </style>

    <style type="text/css">
        .button {
            display: inline-block;
            border-radius: 4px;
            margin-top: 50px;
            padding: 14px 32px;
            background-color: #6a69e4;
            font-family: "Circular-Std", sans-serif;
            font-size: 18px;
            font-weight: 500;
            color: #ffffff;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            text-transform: uppercase;
        }

        .thank-you {
            font-family: "My Sunshine", sans-serif;
            font-size: 36px;
            color: #6a69e4;
            text-align: center;
        }

        .social-link {
            text-decoration: none;
            display: inline-block;
            padding: 0 12px;
        }
        .content p {
            line-height: 10px;
        }
        .content strong {
            font-weight: 700;
            color: #000000;
        }
    </style>
</head>

<body style="margin: 0; padding: 0">
    <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td style="padding: 10px 0 30px 0">
                <table align="center" border="0" cellpadding="0" cellspacing="0" width="90%"
                    style="border-collapse: collapse">
                    <tr style="border-bottom: 1px solid #e1e4e7">
                        <td align="center" style="padding: 40px 0 30px 0; color: #153643">
                            <img src="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/images/indigo.png"
                                alt="Indigo" width="100" height="auto" style="display: block" />
                        </td>
                    </tr>

                    <tr>
                        <td align="center" style="padding: 50px 0; color: #153643">
                            <img src="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/images/registration.png"
                                alt="registration" width="100" height="auto" style="display: block" />
                        </td>
                    </tr>
                    
                    <tr>
                        <td>
                            <p><strong style="color: #000000">Hospital Name:</strong> ${parentHospital}</p>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <table align="center" width="100%">
                                <tr>
                                    <td>
                                        <div class="content">
                                            <p><strong>User:</strong> ${name}</p>
                                            <p><strong>IP Address:</strong> ${ipAddress}</p>
                                            <p><strong>Date/Time:</strong> ${date}</p>
                                            <p><strong>Device Detail:</strong> ${operatingSystem}, ${platform}</p>
                                            <p><strong>Browser:</strong> ${browser}</p>
                                        </div>

                                        <div style="max-width: 700px; margin-top: 40px">
                                            <p>
                                                If you recently signed in and recognize the user name, you may disregard this email.
                                            </p>
                                            <p style="margin-top: 10px">
                                                If you did not recently sign in, you should immediately change your password or contact admin.
                                            </p>
                                            <p style="margin-top: 20px">Regards</p>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                   

                    <tr>
                        <td style="padding-top: 80px">
                            <p class="thank-you">
                                Thank you from the Indigo team
                                <span style="display: inline-block"><img
                                        src="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/images/love.png"
                                        alt="love" width="35" height="35" /></span>
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

</body>
    `;
};
export default NewDevice;
