/* eslint max-len: ["error", { "code": 200 }] */

const mail = ({ patient, organization, dateCreated, isEnv }) => {
    return `
    <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Indigo Email Template</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      rel="preload"
      href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.woff"
      as="font"
      type="font/woff"
      crossorigin
    />
    <link
      rel="preload"
      href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.woff"
      as="font"
      type="font/woff"
      crossorigin
    />
    <link
      rel="preload"
      href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.woff"
      as="font"
      type="font/woff"
      crossorigin
    />
    <link
      rel="preload"
      href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.woff"
      as="font"
      type="font/woff"
      crossorigin
    />

    <style type="text/css">
      @font-face {
        font-family: "Circular-Std";
        font-weight: 400;
        font-display: swap;
        src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.eot"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.woff") format("woff"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.ttf") format("truetype"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.svg#webfont") format("svg");
      }

      @font-face {
        font-family: "Circular-Std";
        font-weight: 500;
        font-display: swap;
        src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.eot"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.woff") format("woff"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.ttf") format("truetype"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.svg#webfont") format("svg");
      }

      @font-face {
        font-family: "Circular-Std";
        font-weight: 700;
        font-style: normal;
        font-display: swap;
        src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.eot"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.woff") format("woff"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.ttf") format("truetype"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.svg#webfont") format("svg");
      }

      @font-face {
        font-family: "My Sunshine";
        font-weight: normal;
        font-style: normal;
        font-display: swap;
        src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.eot"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.woff") format("woff"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.ttf") format("truetype"),
          url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.svg#webfont") format("svg");
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
          "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
          "Helvetica Neue", sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      h1,
      h2,
      h3,
      h4 {
        font-family: "Circular-Std", sans-serif;
        font-weight: 500;
        font-size: 24px;
        color: #000000;
        margin-top: 0;
        letter-spacing: 1px;
      }

      p,
      ol,
      li,
      ul {
        font-family: "Circular-Std", sans-serif;
        font-weight: 400;
        font-size: 16px;
        color: #97a3b6;
        line-height: 40px;
        letter-spacing: 0.5px;
        margin-top: 0;
      }

      table {
        text-align: center;
      }

      .table__col {
        padding: 18px 0;
        border-bottom: 1px solid #e1e4e7;
      }

      .normal-line {
          line-height: normal;
          text-align: left;
      }

      .color-black {
          color: #000000;
      }
    </style>

    <style type="text/css">
      .button {
        display: inline-block;
        border-radius: 4px;
        margin-top: 50px;
        padding: 14px 32px;
        background-color: #6a69e4;
        font-family: "Circular-Std", sans-serif;
        font-size: 18px;
        font-weight: 500;
        color: #ffffff;
        cursor: pointer;
        text-decoration: none;
        text-align: center;
        text-transform: uppercase;
      }

      .thank-you {
        font-family: "My Sunshine", sans-serif;
        font-size: 36px;
        color: #6a69e4;
      }

      .social-link {
        text-decoration: none;
        display: inline-block;
        padding: 0 12px;
      }
    </style>
  </head>
  <body style="margin: 0; padding: 0">
  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td style="padding: 10px 0 30px 0">
        <table
          align="center"
          border="0"
          cellpadding="0"
          cellspacing="0"
          width="90%"
          style="border-collapse: collapse"
        >
          <tr style="border-bottom: 1px solid #e1e4e7">
            <td align="center" style="padding: 40px 0 30px 0; color: #153643">
              <img
                src="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/images/indigo.png"
                alt="Indigo"
                width="100"
                height="auto"
                style="display: block"
              />
            </td>
          </tr>

          <tr>
            <td align="center" style="padding: 50px 0; color: #153643">
              <img
                src="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/images/registration.png"
                alt="registration"
                width="100"
                height="auto"
                style="display: block"
              />
            </td>
          </tr>

          <tr>
            <td>
              <table align="center" width="100%">
                <tr>
                  <td>
                    <p style="font-size: 20px; text-align: left;">Dear ${patient.name},</p>
                    <p class="normal-line">
                        Welcome to ${organization.name}! We are committed to
                        providing you with the best possible care and are here
                        to support you every step of the way. 
                    </p>
                    <p class="normal-line">
                        Your Registration is Complete <br />
                        We are pleased to inform you that your registration with
                        our hospital has been completed.  
                    </p>

                      <table
                        align="left"
                        width="100%"
                        style="
                          text-align: left;
                          margin-top: 50px;
                          color: #97a3b6;
                          border-collapse: collapse;
                        "
                      >
                      <tr>
                        <td class="table__col">Patient's Name</td>
                        <td
                          class="table__col"
                          style="
                            color: #6a69e4;
                            font-weight: 500;
                            text-align: right;
                          "
                        >
                          ${patient.name}
                        </td>
                       </tr>
                       <tr>
                          <td class="table__col">MRN number</td>
                          <td
                            class="table__col"
                            style="
                              color: #6a69e4;
                              font-weight: 500;
                              text-align: right;
                            "
                          >
                            ${patient.mrn}
                          </td>
                        </tr>
                        <tr>
                        <td class="table__col">Phone Number</td>
                        <td
                          class="table__col"
                          style="
                            color: #6a69e4;
                            font-weight: 500;
                            text-align: right;
                          "
                        >
                          ${patient.phoneNumber}
                        </td>
                      </tr>
                        ${
                            patient.patientPlan
                                ? `
                          <tr>
                            <td class="table__col">Plan name</td>
                            <td
                              class="table__col"
                              style="
                                color: #6a69e4;
                                font-weight: 500;
                                text-align: right;
                              "
                            >
                              ${patient.patientPlan.name}
                            </td>
                          </tr>`
                                : ``
                        }
                      <tr>
                      <td class="table__col">Date</td>
                      <td
                        class="table__col"
                        style="
                          color: #6a69e4;
                          font-weight: 500;
                          text-align: right;
                        "
                      >
                        ${dateCreated}
                      </td>
                    </tr>
                      </table>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                      <div style="max-width: 700px; margin: 0 auto;">
                          <p style="text-align: left; margin-top: 40px;">
                              <strong style="margin-top: 40px;">What Happens Next:</strong> <br />
                          <ol type="1" style="text-align: left;">
                              <li class="normal-line color-black">Appointment Confirmation:</li>
                              <p class="normal-line">
                                  You will receive a confirmation email or SMS with the details if you have
                                  scheduled an appointment. If you haven't scheduled an appointment yet,
                                  please contact us ${organization.phoneNumber} or visit us at our location.
                              </p>
                              <li class="normal-line color-black">Access to Your Medical Records:</li>
                              <p class="normal-line">
                                  Your medical records will be securely stored and managed in our system.
                                  You will be able to access your records online via our patient portal.
                                  Please check your email for instructions on how to set up your portal account.
                              </p>
                              <li class="normal-line color-black">Consent to Receive Information:</li>
                              <p class="normal-line">
                                  In line with GDPR, we may send you important updates about your care,
                                  appointment reminders, and health-related information. If you have
                                  not yet provided consent, please do so 
                              </p>
                          </ol> 

                          <div class="normal-line">
                              <strong class="color-black">Staying in Touch:</strong>
                          </div>
                          <p class="normal-line">
                              If you have any questions or need assistance before your visit,
                              please don't hesitate to contact us at ${organization.phoneNumber}. 
                              We are here to help you with any inquiries you may have. 
                          </p>

                          <p class="normal-line">
                              Thank you for choosing ${organization.name}. We look forward to serving you and
                              ensuring your health and well-being. 
                          </p>

                          <p class="normal-line">
                              Warm regards, <br />
                              ${organization.name}
                          </p>
                      </div>
                  </td>
              </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    ${
        !isEnv("production")
            ? `
    <tr>
      <td>
        <div style="text-align: center; margin-bottom: 30px;">
          <a style=" color: #C5C5C5" href="${process.env.WEB_APP_URL}/unsubscribe?_id=${patient._id}">
            Unsubscribe
          </a>
        </div>
      </td>
    </tr>`
            : ""
    }
  </table>
</body>`;
};

export default mail;
