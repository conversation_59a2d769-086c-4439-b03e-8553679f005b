/* eslint-disable max-len */

/**
 * Wallet Transfer OTP Email Template
 * @param {Object} params
 * @param {string} params.senderName - Name of the patient sending the transfer
 * @param {string} params.amount - Transfer amount with currency symbol
 * @param {string} params.receiverName - Name of the patient receiving the transfer
 * @param {string} params.operatorName - Name of the operator initiating the transfer
 * @param {string} params.operatorLocation - Location/branch of the operator
 * @param {string} params.otpCode - The OTP code
 * @param {string} params.duration - OTP expiration duration
 * @param {string} params.contactNumber - Operator location contact number
 * @param {string} params.contactEmail - Operator location email
 * @param {string} params.hospitalName - Parent hospital name
 * @returns {string} HTML email template
 */
const walletTransferOTP = ({
    senderName,
    amount,
    receiverName,
    operatorName,
    operatorLocation,
    otpCode,
    duration,
    contactNumber,
    contactEmail,
    hospitalName
}) => {
    return `
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Wallet Transfer Verification Required</title>
        <link
            rel="preload"
            href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.woff"
            as="font"
            type="font/woff"
            crossorigin
        />
        <link
            rel="preload"
            href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.woff"
            as="font"
            type="font/woff"
            crossorigin
        />
        <link
            rel="preload"
            href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.woff"
            as="font"
            type="font/woff"
            crossorigin
        />
        <style>
            @font-face {
                font-family: "MySunshine";
                src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.woff")
                    format("woff");
                font-weight: normal;
                font-style: normal;
            }
            @font-face {
                font-family: "CircularStd";
                src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.woff")
                    format("woff");
                font-weight: normal;
                font-style: normal;
            }
            @font-face {
                font-family: "CircularStd";
                src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.woff")
                    format("woff");
                font-weight: 500;
                font-style: normal;
            }
            body {
                margin: 0;
                padding: 0;
                font-family: "CircularStd", Arial, sans-serif;
                background-color: #f8f9fa;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .header {
                background: linear-gradient(135deg, #6A69E4 0%, #5A59D4 100%);
                padding: 40px 30px;
                text-align: center;
                color: white;
            }
            .header h1 {
                margin: 0;
                font-size: 24px;
                font-weight: 500;
                font-family: "MySunshine", Arial, sans-serif;
            }
            .content {
                padding: 40px 30px;
                line-height: 1.6;
                color: #333333;
            }
            .greeting {
                font-size: 18px;
                margin-bottom: 20px;
                color: #2c3e50;
            }
            .otp-section {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border: 2px solid #6A69E4;
                border-radius: 12px;
                padding: 25px;
                text-align: center;
                margin: 30px 0;
            }
            .otp-label {
                font-size: 14px;
                color: #666;
                margin-bottom: 10px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            .otp-code {
                font-size: 32px;
                font-weight: 500;
                color: #6A69E4;
                letter-spacing: 4px;
                margin: 10px 0;
                font-family: "CircularStd", monospace;
            }
            .otp-expiry {
                font-size: 12px;
                color: #e74c3c;
                margin-top: 10px;
            }
            .transfer-details {
                background-color: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin: 25px 0;
            }
            .detail-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 12px;
                padding-bottom: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            .detail-row:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }
            .detail-label {
                color: #666;
                font-weight: 500;
            }
            .detail-value {
                color: #2c3e50;
                font-weight: 500;
                text-align: right;
            }
            .warning {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 6px;
                padding: 15px;
                margin: 20px 0;
                color: #856404;
            }
            .contact-info {
                background-color: #e8f4fd;
                border-radius: 6px;
                padding: 15px;
                margin: 20px 0;
                font-size: 14px;
            }
            .footer {
                background-color: #f8f9fa;
                padding: 30px;
                text-align: center;
                border-top: 1px solid #e9ecef;
            }
            .footer p {
                margin: 5px 0;
                color: #666;
                font-size: 14px;
            }
            .hospital-name {
                font-weight: 500;
                color: #6A69E4;
            }
            .powered-by {
                font-size: 12px;
                color: #999;
                margin-top: 15px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔐 Wallet Transfer Verification</h1>
            </div>
            
            <div class="content">
                <div class="greeting">
                    Dear ${senderName},
                </div>
                
                <p>
                    We are reaching out to confirm a wallet transfer request initiated from your wallet for 
                    <strong>${amount}</strong> to <strong>${receiverName}</strong> by <strong>${operatorName}</strong> 
                    from <strong>${operatorLocation}</strong>.
                </p>
                
                <div class="transfer-details">
                    <div class="detail-row">
                        <span class="detail-label">Transfer Amount:</span>
                        <span class="detail-value">${amount}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Recipient:</span>
                        <span class="detail-value">${receiverName}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Initiated By:</span>
                        <span class="detail-value">${operatorName}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Location:</span>
                        <span class="detail-value">${operatorLocation}</span>
                    </div>
                </div>
                
                <p>
                    If you <strong>APPROVE</strong> this transaction, please provide the following One-Time Password (OTP) 
                    to the operator to proceed:
                </p>
                
                <div class="otp-section">
                    <div class="otp-label">Your OTP Code</div>
                    <div class="otp-code">${otpCode}</div>
                    <div class="otp-expiry">This verification code will expire in ${duration}</div>
                </div>
                
                <div class="warning">
                    <strong>⚠️ Security Notice:</strong> If you did not initiate or approve this transaction, 
                    please contact us immediately for assistance.
                </div>
                
                <div class="contact-info">
                    <strong>Need Help?</strong><br>
                    📞 Contact: ${contactNumber}<br>
                    ✉️ Email: ${contactEmail}
                </div>
                
                <p>Thank you for helping us keep your account secure.</p>
            </div>
            
            <div class="footer">
                <p>Regards,</p>
                <p class="hospital-name">${hospitalName}</p>
                <p class="powered-by">Powered by IndigoEMR</p>
            </div>
        </div>
    </body>
    `;
};

export default walletTransferOTP;
