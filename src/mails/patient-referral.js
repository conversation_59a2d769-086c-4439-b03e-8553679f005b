/* eslint max-len: ["error", { "code": 200 }] */

const patientReferralMail = ({
    consultant<PERSON>ame,
    hospitalname,
    patient,
    specialty,
    reasonForReferral,
    organization,
    location,
    branch,
    isEnv
}) => {
    return `
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Indigo Email Template</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<link rel="preload"
    href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.woff"
    as="font" type="font/woff" crossorigin />
<link rel="preload"
    href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.woff" as="font"
    type="font/woff" crossorigin />
<link rel="preload"
    href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.woff"
    as="font" type="font/woff" crossorigin />
<link rel="preload"
    href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.woff" as="font"
    type="font/woff" crossorigin />

<style type="text/css">
    @font-face {
        font-family: "Circular-Std";
        font-weight: 400;
        font-display: swap;
        src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.eot"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.woff"
            ) format("woff"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.ttf"
            ) format("truetype"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.svg#webfont"
            ) format("svg");
    }

    @font-face {
        font-family: "Circular-Std";
        font-weight: 500;
        font-display: swap;
        src:
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.eot"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.woff"
            ) format("woff"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.ttf"
            ) format("truetype"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.svg#webfont"
            ) format("svg");
    }

    @font-face {
        font-family: "Circular-Std";
        font-weight: 700;
        font-style: normal;
        font-display: swap;
        src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.eot"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.woff ") format("woff"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.ttf"
            ) format("truetype"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.svg#webfont"
            ) format("svg");
    }

    @font-face {
        font-family: "My Sunshine";
        font-weight: normal;
        font-style: normal;
        font-display: swap;
        src: url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.eot"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.woff"
            ) format("woff"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.ttf"
            ) format("truetype"),
            url("https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.svg#webfont"
            ) format("svg");
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
            "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
            "Helvetica Neue", sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    h1,
    h2,
    h3,
    h4 {
        font-family: "Circular-Std", sans-serif;
        font-weight: 500;
        font-size: 24px;
        color: #000000;
        margin-top: 0;
        letter-spacing: 1px;
    }

    p,
    ol,
    li,
    ul {
        font-family: "Circular-Std", sans-serif;
        font-weight: 400;
        font-size: 16px;
        color: #181818;
        line-height: 40px;
        letter-spacing: 0.5px;
        margin-top: 0;
    }

    table {
        text-align: center;
    }

    .table__col {
        padding: 18px 0;
        border-bottom: 1px solid #e1e4e7;
    }

    .normal-line {
        line-height: normal;
        text-align: left;
    }

    .color-black {
        color: #000000;
    }
</style>
</head>
<body style="margin: 0; padding: 0">
<table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
        <td style="padding: 10px 0 30px 0">
            <table
                align="center"
                border="0"
                cellpadding="0"
                cellspacing="0"
                width="90%"
                style="border-collapse: collapse"
            >
                <tr style="border-bottom: 1px solid #e1e4e7">
                    <td align="center" style="padding: 40px 0 30px 0; color: #153643">
                        <img
                            src="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/images/indigo.png"
                            alt="Indigo"
                            width="100"
                            height="auto"
                            style="display: block"
                        />
                    </td>
                </tr>

                <tr>
                    <td align="center" style="padding: 50px 0; color: #153643">
                        <img
                            src="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/images/registration.png"
                            alt="registration"
                            width="100"
                            height="auto"
                            style="display: block"
                        />
                    </td>
                </tr>

                <tr>
                    <td>
                        <table align="center" width="100%">
                            <tr>
                                <td>
                                    <div style="max-width: 700px; margin: 0 auto">
                                        <p style="font-size: 20px; text-align: left">Dear ${patient.name},</p>
                                        <p class="normal-line">
                                            We hope this message finds you well. After reviewing your recent consultations and medical history,
                                            we believe that it would be beneficial for you to see a specialist for further evaluation and treatment.
                                            Therefore, we are referring you to ${consultantName} at ${hospitalname}. 
                                        </p>
                                        <p class="color-black normal-line">Referral Details: </p>
                                        <ul>
                                            <li class="normal-line"><bold class="color-black">Patient MRN:</bold> ${patient.mrn}</li>
                                            <li class="normal-line"><bold class="color-black">Specialist's Name:</bold> ${consultantName}</li>
                                            <li class="normal-line"><bold class="color-black">Specialty:</bold> ${specialty}</li>
                                            <li class="normal-line">
                                                <bold class="color-black">Location:</bold>
                                                ${location.address.exact} ${location.address.city}
                                                ${location.address.state} ${location.address.country}
                                            </li>
                                        </ul>

                                        <div class="normal-line">
                                            <bold class="color-black">Next Steps:</bold>
                                            <p class="normal-line" style="margin: 10px 0 !important;">
                                                <bold class="normal-line color-black">Appointment Scheduling:</bold> <br />
                                                Please contact the specialist's office to schedule your appointment. 
                                            </p>
                                            <bold class="color-black normal-line">Why This Referral Is Important:</bold>
                                            <p class="normal-line">${reasonForReferral}</p>

                                            <bold class="color-black normal-line">Coordination of Care:</bold>
                                            <p class="normal-line">
                                                We will remain in close communication with the specialist to ensure a coordinated approach
                                                to your care. After your visit, feel free to contact us with any questions or concerns. 
                                            </p>

                                            <bold class="color-black normal-line">Questions or Assistance:</bold> 
                                            <p class="normal-line">
                                                If you have any questions about this referral, please don’t hesitate to contact us at
                                                ${branch.phoneNumber}. We are here to support you throughout this process. <br /><br />

                                                Thank you for your trust in us, and we look forward to continuing to provide you
                                                with the highest level of care. <br /> <br />
                                                Warm regards, <br />
                                                ${organization.name}
                                            </p>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    ${
        !isEnv("production")
            ? `
    <tr>
        <td>
            <div style="text-align: center; margin-bottom: 30px;">
            <a style=" color: #C5C5C5" href="${process.env.WEB_APP_URL}/unsubscribe?_id=${patient._id}">
                Unsubscribe
            </a>
            </div>
        </td>
    </tr>`
            : ""
    }
</table>
</body>
    `;
};

export default patientReferralMail;
