const refundRequest = ({ fullname, url, attachment, name, mrn, amount, comment }) => {
    return `
  <head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Indigo Email Template</title>
  <link
    rel="preload"
    href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.woff"
    as="font"
    type="font/woff"
    crossorigin
  />
  <link
    rel="preload"
    href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.woff"
    as="font"
    type="font/woff"
    crossorigin
  />
  <link
    rel="preload"
    href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.woff"
    as="font"
    type="font/woff"
    crossorigin
  />
  <link
    rel="preload"
    href="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.woff"
    as="font"
    type="font/woff"
    crossorigin
  />
  <style type="text/css">
    @font-face {
      font-family: 'Circular-Std';
      font-weight: 400;
      font-display: swap;
      src: url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.eot'),
        url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.woff')
          format('woff'),
        url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.ttf')
          format('truetype'),
        url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Book.svg#webfont')
          format('svg');
    }

    @font-face {
      font-family: 'Circular-Std';
      font-weight: 500;
      font-display: swap;
      src: url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.eot'),
        url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.woff')
          format('woff'),
        url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.ttf')
          format('truetype'),
      url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Medium.svg#webfont')
          format('svg');
    }

    @font-face {
      font-family: 'Circular-Std';
      font-weight: 700;
      font-style: normal;
      font-display: swap;
      src: url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.eot'),
        url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.woff')
          format('woff'),
        url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.ttf')
          format('truetype'),
        url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/CircularStd-Bold.svg#webfont')
          format('svg');
    }

    @font-face {
      font-family: 'My Sunshine';
      font-weight: normal;
      font-style: normal;
      font-display: swap;
      src: url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.eot'),
        url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.woff')
          format('woff'),
        url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.ttf')
          format('truetype'),
        url('https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/fonts/MySunshineRegular.svg#webfont')
          format('svg');
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell',
        'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    h1,
    h2,
    h3,
    h4 {
      font-family: 'Circular-Std', sans-serif;
      font-weight: 500;
      font-size: 24px;
      color: #000000;
      margin-top: 0;
      letter-spacing: 1px;
    }

    p,
    ol,
    li,
    ul {
      font-family: 'Circular-Std', sans-serif;
      font-weight: 400;
      font-size: 16px;
      color: #97a3b6;
      line-height: 40px;
      letter-spacing: 0.5px;
      margin-top: 0;
    }
  </style>

  <style type="text/css">
    .button {
      display: inline-block;
      border-radius: 4px;
      margin-top: 50px;
      padding: 14px 32px;
      background-color: #6a69e4;
      font-family: 'Circular-Std', sans-serif;
      font-size: 18px;
      font-weight: 500;
      color: #ffffff;
      cursor: pointer;
      text-decoration: none;
      text-align: center;
      text-transform: uppercase;
    }

    .thank-you {
      font-family: 'My Sunshine', sans-serif;
      font-size: 36px;
      color: #6a69e4;
    }

    .social-link {
      text-decoration: none;
      display: inline-block;
      padding: 0 12px;
    }

    .tr_item {
      border-bottom: 1px solid #e1e4e7;
    }
    .tr_item td {
      padding-top: 15px;
      padding-bottom: 5px;
    }
  </style>
</head>
<body>
  <div align="center">
    <table border="0" cellpadding="0" cellspacing="0" align="" width="100%" style="max-width:700px;">
      <tbody>
        <tr>
          <td>
            <table border="0" cellpadding="0" align="center" cellspacing="0" width="100%">
              <tr>
                <td style="padding: 0 0 30px 0">
                  <table
                    align="center"
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                    width="100%"
                    style="border-collapse: collapse;text-align: center;"
                  >
                    <tr style="border-bottom: 1px solid #e1e4e7">
                      <td align="center" style="padding: 25px; color: #153643">
                        <img
                          src="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/images/indigo.png"
                          alt="Indigo"
                          width="68"
                          height="43"
                          style="display: block"
                        />
                      </td>
                    </tr>

                    <tr>
                      <td align="center" style="padding: 60px 0; color: #153643">
              <img
                   src="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/images/registration.png"
                          alt="registration"
                          width="82"
                          height="82"
                          style="display: block"
                        />
                      </td>
                    </tr>

                    <tr>
                      <td></td>
                    </tr>

                    <tr>
                      <td>
                        <table align="center" width="100%">
                          <tr>
                            <td>
                              <p style="font-size: 20px">Hello ${fullname},</p>

                              <div style="max-width: 700px; margin: 0 auto">
                                <p>
                                  ${name} with MRN (${mrn}) has requested for refund of ${amount}.
                                </p>
                              ${
                                  comment
                                      ? `<div>

                                <p>
                                <strong>Comment</strong>
                                </p>
                                <p>
                                ${comment}
                                </p>
                                </div>`
                                      : ""
                              }

                                ${attachment ? `<img src=${attachment} />` : ""}

                                <div style="margin: 30px auto 30px auto;">
                                  <a
style="background:#6A69E4;width: max-content;padding: 12px 38px; border-radius: 4px;color:white;text-decoration: none;"
                                    href=${url}
                                  >
                                    Login to approve or decline
                                  </a>
                                </div>

                                <p>
                                  REGARDS
                                </p>
                              </div>
                            </td>
                          </tr>
                        </table>


                      </td>
                    </tr>
                    <tr>
                      <td style="padding-top: 80px">
                        <p class="thank-you">
                          Thank you from the Indigo team
                          <img
                            src="https://indigo-emr.s3.eu-west-1.amazonaws.com/assets/email-template/images/love.png"
                            alt="love"
                            width="37"
                            height="33"
                          />
                        </p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</body>
  `;
};
export default refundRequest;
