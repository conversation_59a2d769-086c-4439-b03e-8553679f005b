import { error, success } from "helpers/response";
import { paginate } from "base/helper";
import { startSession, Types } from "mongoose";
import { composeDoc, generateFilter } from "app/comments/helper";
import Comment from "app/comments/model";

const { ObjectId } = Types;

export const create = async (req, res) => {
    const { replyTo } = req.body;
    try {
        let result;
        const doc = await composeDoc(req);
        const session = await startSession();
        await session.withTransaction(async () => {
            result = await Comment.create([doc], { session });
            if (replyTo) {
                await Comment.updateOne({ _id: new ObjectId(replyTo) }, { $inc: { replyCount: 1 } }, { session });
            }
        });
        await session.endSession();
        return success(res, 201, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetch = async (req, res) => {
    const { limit, page } = req.query;
    try {
        const filter = generateFilter(req);
        const options = { page, limit, modelName: "Comment", filter };
        const result = await paginate(options);
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const resolve = async (req, res) => {
    try {
        const query = { resolved: true };
        const filter = { _id: req.params._id };
        const comment = await Comment.findOneAndUpdate(filter, query, { new: true });

        return success(res, 200, comment);
    } catch (err) {
        return error(res, 500, err);
    }
};
