import Notification from "app/notifications/model";
import { error, success } from "helpers/response";
import { Socket } from "helpers/socket";
import Staff from "settings/staff/model";
import { composeFilter } from "./helper";

export const markAsRead = async (req, res) => {
    try {
        const result = await Notification.findByIdAndUpdate(req.params._id, { read: true }, { new: true });
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const markMultipleAsRead = async (req, res) => {
    const { ids } = req.body;
    const {
        parentOrganizationId,
        operator: { _id: topic }
    } = req.user.currentLocation;
    try {
        const result = await Notification.updateMany(
            {
                read: false,
                parentOrganizationId: parentOrganizationId,
                topic: topic,
                _id: { $in: ids }
            },
            { read: true },
            { new: true }
        );

        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const deleteMultipleNotification = async (req, res) => {
    try {
        const { ids } = req.body;
        const {
            parentOrganizationId,
            operator: { _id: topic }
        } = req.user.currentLocation;
        const result = await Notification.deleteMany({
            parentOrganizationId: parentOrganizationId,
            topic: topic,
            _id: { $in: ids }
        });
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const deleteNotification = async (req, res) => {
    try {
        await Notification.findByIdAndDelete(req.params._id, { new: true });
        return success(res, 200, "Notification successfully deleted");
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetch = async (req, res) => {
    try {
        const notifications = await Notification.find(
            { ...composeFilter(req) },
            {},
            { sort: "-createdAt", lean: true }
        );
        return success(res, 200, notifications);
    } catch (err) {
        return error(res, 500, err);
    }
};

/**
 * Rooms - Socket rooms
 * Event - Socket events
 * Doc - Notification model
 */
export const sendNotification = async (payload) => {
    try {
        const { event, data, doc, general = false, parentOrganizationId } = payload;
        let { rooms } = payload;
        let filter = {};
        if (!general) {
            filter = {
                $or: [{ "sections.name": { $in: [...rooms] } }, { "locations.modules": { $in: [...rooms] } }],
                parentOrganizationId
            };
        }
        const staffList = await Staff.find(filter, "_id parentOrganizationId", { lean: true });
        const docs = staffList.map((staff) => ({
            ...doc,
            topic: staff._id,
            parentOrganizationId: staff.parentOrganizationId
        }));
        if (!rooms?.length) rooms = staffList.map((staff) => String(staff._id));
        if (!general && rooms.length) rooms = rooms.map((room) => `${room}-${parentOrganizationId}`);
        Socket.send(rooms, event, data);
        return Notification.insertMany(docs);
    } catch (err) {
        return err;
    }
};
