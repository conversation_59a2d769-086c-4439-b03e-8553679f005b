import { addDays } from "date-fns";
import { idNameSchema } from "helpers/constants";
import { model, Schema } from "mongoose";

const schema = new Schema(
    {
        title: { type: "String", required: true },
        body: { type: "String", required: true },
        topic: { type: "String", required: true, index: true },
        section: { type: "String", index: true },
        sectionId: { type: "String", index: true },
        read: { type: "Boolean", default: false },
        parentOrganizationId: { type: "String", required: true, index: true },
        operatorTransferredTo: idNameSchema,
        expiresAt: {
            type: Date,
            select: false,
            required: true,
            default: function () {
                return addDays(new Date(), 15);
            }
        }
    },
    { timestamps: true }
);

schema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });
const Model = model("Notification", schema);
export default Model;
