import { isAuthorized } from "base/request";
import { Router } from "express";
import {
    deleteMultipleNotification,
    deleteNotification,
    fetch,
    markAsRead,
    markMultipleAsRead
} from "app/notifications/controller";
import { validator } from "helpers/config";
import { fetchSchema } from "./request";

const router = Router();

router.get("/notifications", isAuthorized([]), validator.query(fetchSchema), fetch);
router.post("/notifications/:_id/read", isAuthorized([]), markAsRead);
router.delete("/notifications/:_id/delete", isAuthorized([]), deleteNotification);
router.post("/notifications/read", isAuthorized([]), markMultipleAsRead);
router.post("/notifications/delete", isAuthorized([]), deleteMultipleNotification);

export default router;
