import { escapeSpecialCharacters, objectHasKey } from "helpers/utils";

export const composeFilter = (req) => {
    const { parentOrganizationId, profile } = req.user.currentLocation;
    const { title, section, read } = req.query;

    let filter = {topic: profile._id, deletedAt: null, parentOrganizationId}

    if (title) filter = { ...filter, title: { $regex: new RegExp(`${escapeSpecialCharacters(title.trim())}`, "i") } };
    if (section) filter = { ...filter, section };
    if (objectHas<PERSON>ey(req.query, "read")) filter = { ...filter, read };

    return filter;
}
