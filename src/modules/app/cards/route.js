import { Router } from "express";
import { isAuthorized } from "base/request";
import { get, create, deactivate } from "app/cards/controller";
import { validator } from "helpers/config";
import { createSchema } from "./middleware";

const router = Router();

router.get("/cards", isAuthorized(["super-view"]), get);
router.post("/cards", isAuthorized(["super-create"]), validator.body(createSchema), create);
router.patch("/cards/:_id/deactivate", isAuthorized(["super-edit"]), deactivate);

export default router;
