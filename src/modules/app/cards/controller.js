import Card from "app/cards/model";
import { paymentTypes } from "helpers/constants";
import { error, success } from "helpers/response";
import { nairaToKobo } from "helpers/utils";
import { model } from "mongoose";
import { initTransaction } from "services/pay";
import Organization from "settings/organizations/model";
import throwErrorWithCode from "utils/throw-error";

const getActiveCard = async (parentOrganizationId) => {
    const filter = { isActive: true, parentOrganizationId };
    return Card.findOne(filter).populate("parentOrganizationId", "name email credits").lean();
};

export const confirmOrgHasNoActiveCard = async (parentOrganizationId) => {
    const card = await getActiveCard(parentOrganizationId);
    if (card) throwErrorWithCode("A card already exists for this organization", 409);
};

export const confirmOrgHasAnActiveCard = async (parentOrganizationId) => {
    const card = await getActiveCard(parentOrganizationId);
    if (!card) throwErrorWithCode("No active cards available", 404);
    return card;
};

export const confirmOrgSubscription = async (parentOrganizationId) => {
    const {
        noOfPayments,
        plan: { staffPrice, currency }
    } = await model("Subscription")
        .findOne({
            organization: parentOrganizationId
        })
        .populate(["plan"])
        .lean();

    return { noOfPayments, staffPrice, currency };
};

export const get = async (req, res) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        const card = await getActiveCard(parentOrganizationId);
        return success(res, 200, card);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const create = async (req, res) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        await confirmOrgHasNoActiveCard(parentOrganizationId);
        const { _id, email } = await Organization.findById(parentOrganizationId).lean();
        const { noOfPayments, staffPrice, currency } = await confirmOrgSubscription(parentOrganizationId);
        const payload = { email, currency, metadata: { organization: _id } };
        if (staffPrice !== 0 && noOfPayments === 0) {
            payload.amount = staffPrice;
            payload.metadata = { ...payload.metadata, reason: paymentTypes.changePaymentMethod };
        } else {
            const cardAmount = currency === "USD" ? 1 : 100;
            payload.amount = nairaToKobo(cardAmount);
            payload.metadata = { ...payload.metadata, reason: paymentTypes.cardRenewalAndSubscriptionPayment };
        }
        payload.reference = `INDIGO-${new Date().getTime()}-${_id}`;
        const response = await initTransaction({ ...payload, ...req.body });
        if (typeof response === "string") {
            return error(res, 500, `Paystack Error ${response}`);
        }
        return success(res, 201, response);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const deactivate = async (req, res) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        const { _id } = req.params;
        const card = await Card.findOneAndUpdate(
            { parentOrganizationId, _id },
            { $set: { isActive: false } },
            { new: true }
        );
        return success(res, 200, card);
    } catch (err) {
        return error(res, 500, err);
    }
};
