/* eslint-disable camelcase */
import { initializePayment } from "app/payments/controller";
import { sendWelcomeEmails } from "app/payments/helper";
import SubscriberPlan from "control/plans/model";
import Patient from "frontdesk/patients/model";
import { error, success } from "helpers/response";
import changePlanMail from "mails/change-plan";
import subscriptionCancelledMail from "mails/subscription-cancelled";
import { startSession } from "mongoose";
import { sendMail } from "services/mail";
import { createOrganization } from "settings/organizations/controller";
import Organization from "settings/organizations/model";
import Staff from "settings/staff/model";
import {
    composeOrganizationCredits,
    composeOrganizationCreditsWrites,
    composePatientsWrites,
    duePatientsPipeline,
    notifyBeforeDisconnection,
    notifyDaysBeforeCharge,
    notifyOnChargeDay
} from "./helper";
import SubscriptionService from "./service";

/**
 * @description creates a new subscription for new users.
 * This route should only be taken if the organization *does not* already exist.
 * It creates a new organization, user, and staff, and a subscription plan as provided in the request payload.
 * @param {Object} req
 * @param {Object} res
 * @returns {Object} Payment Checkout link as returned by the payment processor
 */
export const create = async (req, res) => {
    try {
        const session = await startSession();
        const { plan } = res.locals;
        let org;
        let subscription;
        await session.withTransaction(async () => {
            const owner = { ...req.body.owner, password: `ingido${new Date().getTime()}` };
            let staffLicensesPurchased = 0;
            if (plan.staffPrice === 0) staffLicensesPurchased = plan.maxStaffCount;
            org = await createOrganization({ ...req.body, staffLicensesPurchased, owner }, session);
            subscription = await SubscriptionService.create(
                {
                    organization: org[0]._id,
                    plan: plan._id,
                    interval: req.body.paymentDetails.metadata.interval
                },
                session
            );
        });
        await session.endSession();
        if (org[0]._id) {
            await sendWelcomeEmails(req.body.owner, org[0]);
            res.locals.record.currency = plan.currency;
            res.locals.record.metadata.organization = org[0]._id;
            res.locals.record.metadata.subscriptionId = subscription._id;
            return initializePayment(req, res);
        }
        return error(res, 500, `Server error. Please try again`);
    } catch (err) {
        return error(res, 500, err);
    }
};

/**
 * @description cancels an active subscribtion.
 * @param {*} req params._id - subscriptionId
 * @param {*} res
 * @returns object containing cancelled subscription
 */
export const cancel = async (req, res) => {
    try {
        const { _id } = req.params;
        const { credits } = req.query;
        const { parentOrganizationId: organization } = req.user.currentLocation;

        const org = await Organization.findOne({ _id: organization }).lean();
        const staff = await Staff.findOne({ parentOrganizationId: org._id, _id: org.registeredBy, isOwner: true });
        let sub = await SubscriptionService.getOne({ _id, organization });

        if (!sub) return error(res, 404, `Subscription not found`);
        if (!staff) return error(res, 404, `Cannot find organization's owner`);

        const fields = credits
            ? { credits: { autoTopUp: false } }
            : { isActive: false, reasonForCancel: req.body.reason };

        sub = await SubscriptionService.update(sub._id, { $set: fields }, { new: true });
        sendMail({
            subject: `${credits ? "PATIENT'S CREDITS AUTO " : null} SUBSCRIPTION CANCELLED`,
            email: org.email,
            body: subscriptionCancelledMail({ fullname: staff.name, hospitalname: org.name })
        });
        return success(res, 200, sub);
    } catch (err) {
        return error(res, 500, err);
    }
};

/**
 * @description CRON job. Triggered everyday. Executes for patients whose next payment date is on or before today
 * @param {*} req
 * @param {*} res
 * @returns string done
 */
export const chargeForPatients = async (req, res) => {
    try {
        const pipeline = duePatientsPipeline();
        const patients = await Patient.aggregate(pipeline);
        if (!patients.length) return success(res, 200, `No patients to charge`);
        const { errors, orgCredits } = composeOrganizationCredits(patients);
        const patientWrites = composePatientsWrites(orgCredits);
        const orgWrites = composeOrganizationCreditsWrites(orgCredits);
        const session = await startSession();
        await session.withTransaction(async () => {
            await Patient.bulkWrite(patientWrites, { session, ordered: false });
            await Organization.bulkWrite(orgWrites, { session, ordered: false });
        });
        await session.endSession();
        return success(res, 202, errors.length ? errors : "done");
    } catch (err) {
        return error(res, 500, err);
    }
};

export const upsertCreditsSubscription = async (req, res) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        let { subscription } = res.locals;
        subscription = await SubscriptionService.update(
            { _id: subscription._id, organization: parentOrganizationId },
            { $set: { credits: { ...req.body } } }
        );
        return success(res, 200, subscription);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetch = async (req, res) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        const populants = [{ path: "organization", select: "name credits staffLicensesPurchased" }, { path: "plan" }];
        const payments = await SubscriptionService.getMany(
            { organization: parentOrganizationId },
            { dontPaginate: true, populate: populants }
        );
        return success(res, 200, payments);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const changePlan = async (req, res) => {
    try {
        const { plan } = req.body;
        const { parentOrganizationId: organization, operator } = req.user.currentLocation;
        const org = await Organization.findOne({ _id: organization }).lean();
        const newPlan = await SubscriberPlan.findOne({ _id: plan, custom: false });
        if (!newPlan) return error(res, 404, `Plan not found`);
        const { maxStaffCount } = newPlan;
        const staffCount = await Staff.countDocuments({ parentOrganizationId: organization, isActive: true });
        if (staffCount >= maxStaffCount)
            return error(res, 405, `Plan's user count has to be higher than ${staffCount}`);
        const currentSubscription = await SubscriptionService.getOne({ organization });
        await currentSubscription.populate("plan").execPopulate();
        const oldPlanName = currentSubscription.plan.name;
        const subscription = await SubscriptionService.update({ organization }, { $set: { plan: req.body.plan } });
        sendMail({
            subject: "Subscription Plan Change",
            email: org.email,
            body: changePlanMail({
                price: `${newPlan.currency} ${newPlan.staffPrice / 100}`,
                range: `${newPlan.minStaffCount} - ${newPlan.maxStaffCount}`,
                renewal: `${newPlan.currency} ${newPlan.unitsPerPatient.existing}`,
                newPlan: newPlan.name,
                oldPlan: oldPlanName,
                operator: operator.name,
                hospitalName: org.name,
                firstRegistration: `${newPlan.currency} ${newPlan.unitsPerPatient.new}`
            })
        });
        return success(res, 200, subscription);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const notify = async (req, res) => {
    try {
        await notifyDaysBeforeCharge();
        await notifyOnChargeDay();
        await notifyBeforeDisconnection();
        return success(res, 200, "done");
    } catch (err) {
        return error(res, 500, err);
    }
};

export const activateSubscription = async (req, res) => {
    try {
        const { _id: organization } = req.params;
        const { interval } = req.body;
        const subscriptionExists = await SubscriptionService.getOne({ organization });
        if (subscriptionExists) return error(res, 400, `A subscription already exists for this organization`);
        const staffCount = await Staff.countDocuments({ parentOrganizationId: organization });
        const plan = await SubscriberPlan.findOne({
            custom: false,
            minStaffCount: { $lte: staffCount },
            maxStaffCount: { $gte: staffCount }
        }).lean();
        if (!plan) return error(res, 404, `No suitable plan found for this organization`);
        const response = await SubscriptionService.create({ organization, interval, plan: plan._id });
        return success(res, 201, response);
    } catch (err) {
        return error(res, 500, err);
    }
};
