import { modules } from "helpers/constants";
import { model, Schema } from "mongoose";

function isMaintenanceOptionRequired() {
    return this.ongoing;
}

const schema = new Schema({
    maintenance: {
        ongoing: { type: "Boolean", default: false },
        section: { type: "String", enum: modules.concat("all"), required: isMaintenanceOptionRequired },
        startTime: { type: "Date", required: isMaintenanceOptionRequired },
        endTime: { type: "Date", required: isMaintenanceOptionRequired }
    }
});
const Model = model("AppSetting", schema);
export default Model;
