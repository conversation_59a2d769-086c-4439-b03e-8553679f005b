import { paymentTypes } from "helpers/constants";
import { error } from "helpers/response";
import Joi from "joi";

export const initializePaymentSchema = Joi.object({
    email: Joi.string().required(),
    amount: Joi.number(),
    metadata: Joi.object().required()
});

export const fetchPaymentsSchema = Joi.object({
    fromDate: Joi.date(),
    toDate: Joi.date().min(Joi.ref("fromDate")),
    page: Joi.number().default(1),
    limit: Joi.number().default(20),
    reason: Joi.string().valid(...Object.values(paymentTypes)),
    status: Joi.string().valid("success", "failed")
});

export const validatePaymentInit = (req, res, next) => {
    const { email, amount, metadata } = req.body;

    if (metadata.staffCount > 100) {
        return error(res, 500, `Please contact our sales team`);
    }
    const reference = `INDIGO-${new Date().getTime()}-${metadata.organization}`;
    res.locals.record = { email, amount, metadata, reference };
    return next();
};

export const validateFetchRequest = (req, res, next) => {
    try {
        const { query } = req;
        if (query.fromDate && query.toDate) {
            query.createdAt = { $gte: query.fromDate, $lte: query.toDate };
            delete query.fromDate;
            delete query.toDate;
        }
        res.locals.filters = req.query;
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const retrySchema = Joi.object({
    type: Joi.string().required()
});

export const topUpSchema = Joi.object({
    amount: Joi.number().required()
});
