/* eslint-disable consistent-return */
/* eslint-disable import/no-cycle */
/* eslint-disable camelcase */
import { confirmOrgHasAnActiveCard, confirmOrgSubscription } from "app/cards/controller";
import Card from "app/cards/model";
import * as PaymentService from "app/payments/service";
import { compileSubscriptionPayload, updateLastPaymentDate, updateSubscription } from "app/subscriptions/helper";
import Subscription from "app/subscriptions/model";
import * as UserService from "app/users/service";
import agenda from "config/agenda";
import JOB from "constants/jobs";
import { differenceInMinutes, set } from "date-fns";
import { paymentTypes, subscriptionIntervals } from "helpers/constants";
import { error, success } from "helpers/response";
import { calcNextPaymentDate, generateToken } from "helpers/utils";
import changePaymentMethodMail from "mails/changed-card";
import checkOutMail from "mails/checkoutLink";
import welcomeMail from "mails/new-organization";
import { Types, startSession } from "mongoose";
import { sendMail } from "services/mail";
import { charge } from "services/pay";
import { addOrganizationCredit } from "settings/organizations/helper";
import OrganizationService from "settings/organizations/service";
import StaffService from "settings/staff/service";
import throwErrorWithCode from "utils/throw-error";
import { PAYMENT_METHOD } from "./constant";

const { RESET_PASSWORD_URL } = process.env;
const { ObjectId } = Types;

export const sendWelcomeEmails = async (customer, organization) => {
    const token = generateToken(customer.email);
    await UserService.update({ username: customer.email }, { token });
    const url = `${RESET_PASSWORD_URL}?token=${token}`;
    sendMail({
        email: customer.email,
        subject: "Welcome to Indigo",
        body: welcomeMail({
            email: customer.email,
            org: organization.name,
            url: url
        })
    });
};

export const handlePaymentStatus = (status, interval, date) => {
    switch (status) {
        case "success":
            return {
                paid: true,
                nextPaymentDate: calcNextPaymentDate(interval, new Date(date))
            };
        case "failed":
            return { paid: false };
        default:
            return { paid: false };
    }
};

export const sendCheckOutMail = (org, response) =>
    sendMail({
        name: org.name,
        email: org.email,
        subject: "INDIGO PAYMENT LINK",
        body: checkOutMail({
            name: org.name,
            org: org.name,
            url: response.authorization_url
        })
    });

export const newPaymentUpdates = (nextPaymentDate) => {
    return {
        $set: {
            nextPaymentDate
        },
        $inc: { noOfPayments: 1 }
    };
};

export const logPayment = async (payload, session) => {
    try {
        const { metadata, reference } = payload;
        const payment = await PaymentService.getOne({ reference, organization: metadata.organization });

        if (payment) {
            await PaymentService.update(payment._id, { $set: { ...metadata, ...payload } }, { session });
            return "Payment Updated";
        }

        await PaymentService.create({ ...metadata, ...payload }, session);
        return "Payment Logged";
    } catch (err) {
        throwErrorWithCode(err.message, 400);
    }
};

/**
 *
 * @param {Object} payload { parentOrganizationId, amount }
 * @param {String} req.parentOrganizationId
 * @param {Integer} req.amount
 * @param {*} res
 * @returns
 */
export const compileTopUpDocs = async (payload) => {
    const { parentOrganizationId, amount, operator } = payload;
    const card = await confirmOrgHasAnActiveCard(parentOrganizationId);
    const { currency } = await confirmOrgSubscription(parentOrganizationId);
    const paymentPayload = {
        email: card.parentOrganizationId.email,
        amount: amount,
        currency,
        reference: `INDIGO-${Date.now()}-${parentOrganizationId}`,
        authorization_code: card.code,
        status: "pending",
        method: PAYMENT_METHOD.AGENT,
        metadata: {
            reason: paymentTypes.creditTopUp,
            organization: parentOrganizationId,
            operator,
            items: [{ description: "Credits auto charge", amount, count: amount / 100 }]
        }
    };
    await logPayment(paymentPayload);
    delete paymentPayload.status;
    return paymentPayload;
};

export const discount = 0.08;

export const applyDiscount = (amount) => amount * (1 - discount);
export const applyYearlyRate = (amount) => amount * 12;
export const calculateTotalLicenseAmount = ({ interval, amount, quantity }) => {
    const total = amount * quantity;
    if (interval === subscriptionIntervals.yearly) {
        return applyDiscount(applyYearlyRate(total));
    }
    return total;
};

/**
 *
 * @param {*} payload { parentOrganizationId, exp_year, exp_month, cardId }
 */
export const scheduleCardDeactivation = async (payload) => {
    const { parentOrganizationId, cardId, exp_month, exp_year } = payload;
    const minutesToExpiry = differenceInMinutes(
        set(new Date(), { year: exp_year, month: Number(exp_month) - 1, date: 29 }),
        new Date()
    );
    await agenda.schedule(
        minutesToExpiry > 1 ? `in ${minutesToExpiry} minutes` : `in 1 minute`,
        JOB.DEACTIVATE_EXPIRED_CARD,
        { parentOrganizationId, _id: cardId }
    );
};

export const handleEMRSubscription = async (doc, res) => {
    try {
        let org;
        const { metadata, status, authorization } = doc;
        const { interval, organization, subscriptionId } = metadata;
        const { exp_month, exp_year, last4, authorization_code, brand, bank, channel } = authorization;

        const { paid, nextPaymentDate } = handlePaymentStatus(status, interval, new Date());
        const session = await startSession();
        await session.withTransaction(async () => {
            await logPayment(doc, session);
            org = await OrganizationService.update(
                organization,
                {
                    $set: {
                        paid,
                        staffLicensesPurchased: metadata.staffCount,
                        "credits.count": Number(metadata?.credits?.amount) || 0
                    }
                },
                { session }
            );

            const card = await new Card({
                parentOrganizationId: org._id,
                code: authorization_code,
                brand,
                bank,
                last4Digits: last4,
                channel,
                expiryDate: `${exp_month}/${exp_year}`
            }).save(session);

            await scheduleCardDeactivation({
                exp_year,
                exp_month,
                parentOrganizationId: org._id,
                cardId: card._id
            });

            if (paid) {
                const updates = newPaymentUpdates(nextPaymentDate, metadata);
                await updateSubscription({ _id: subscriptionId, organization, updates, session }, res);
            }
        });
        await session.endSession();
        return "Payment Recorded"
    } catch (err) {
        return error(res, 500, err);
    }
};

export const changePaymentMethod = async (doc) => {
    const { metadata, authorization, amount } = doc;
    const { organization } = metadata;
    const { exp_month, exp_year, last4, authorization_code, brand, bank, channel } = authorization;
    const org = await OrganizationService.getOne({ _id: new ObjectId(organization) });

    let card;
    const session = await startSession();
    await session.withTransaction(async () => {
        await logPayment(doc, session)
        card = await new Card({
            parentOrganizationId: org._id,
            code: authorization_code,
            brand,
            bank,
            last4Digits: last4,
            channel,
            expiryDate: `${exp_month}/${exp_year}`
        }).save(session);
        await OrganizationService.update(organization, { $inc: { "credits.count": amount } }, { session });
        await updateLastPaymentDate(organization, session);
    })
    await session.endSession();

    await scheduleCardDeactivation({
        exp_year,
        exp_month,
        parentOrganizationId: org._id,
        cardId: card._id
    });

    sendMail({
        subject: "PAYMENT METHOD CHANGED",
        email: org.email,
        body: changePaymentMethodMail({
            org: org.name,
            url: process.env.EMR_URL,
            email: `<EMAIL>`
        })
    });
    return card
};

export const retryEMRBilling = async (req, res) => {
    const payment = await PaymentService.getOne({ _id: req.params._id, status: "failed" });
    if (!payment) return error(res, 500, "Failed payment not found");
    await PaymentService.update(payment._id, { $inc: { retries: 1 } });
    const subscription = await Subscription.findOne({
        _id: new ObjectId(payment.subscriptionId),
        isActive: true
    })
        .populate("plan organization")
        .lean();
    const card = await confirmOrgHasAnActiveCard(subscription.organization._id);
    if (!subscription) return error(res, 404, "There is no active subscription with this _id");
    const { organization, plan } = subscription;
    const staffCount = await StaffService.count({ parentOrganizationId: organization?._id });
    const payload = compileSubscriptionPayload({
        ...subscription,
        staffCount,
        email: organization.email,
        organization: organization._id,
        card: card?.code,
        currency: plan.currency,
        staffPrice: plan.staffPrice
    });
    payload.reference = `INDIGO-${Date.now()}-${organization?._id}`;
    const response = await charge(payload);
    if (typeof response === "string") return error(res, 500, `Paystack Error: ${response}`);
    await PaymentService.update(payment._id, { status: "pending" });
    return success(res, 200, response);
};

/**
 * @description Webhook event handler. Adjusts patients licences when credits are purchased.
 * @param {Object} doc Response data from payment processor
 * @param {*} res
 * @returns {Object} {statuscode; 200|500, message, response|error}
 */
const handleCreditTopUp = async (doc) => {
    try {
        const { metadata, amount } = doc;
        const { organization, operator } = metadata;
        const query = { amount, organization, operator };

        let response;
        const session = await startSession();
        await session.withTransaction(async () => {
            response = await addOrganizationCredit(query);
            await logPayment(doc);
        })
        await session.endSession();

        return response;
    } catch (err) {
        throwErrorWithCode(err.message, 400)
    }
};

export const handlePaymentTypes = async (paymentType, doc) => {
    switch (paymentType) {
        case paymentTypes.EMRSubscription:
            return handleEMRSubscription(doc);
        case paymentTypes.changePaymentMethod:
            return changePaymentMethod(doc);
        case paymentTypes.creditTopUp:
            return handleCreditTopUp(doc);
        case paymentTypes.cardRenewalAndSubscriptionPayment:
            return changePaymentMethod(doc);
        default:
            return "OK"
    }
};

/**
 * Returns query to filter progress notes by
 * @function composeFilter
 * @param {Object} dto
 * @returns {Object}
 */
export const composeFilter = (dto) => {
    const { fromDate, toDate, organization } = dto;
    const query = { organization };
    if (fromDate && toDate) query.createdAt = { $gte: fromDate, $lte: toDate };
    return query;
};
