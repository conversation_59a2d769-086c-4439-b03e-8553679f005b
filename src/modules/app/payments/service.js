import paginate from "utils/pagination";
import { composeFilter } from "./helper";
import Payment from "./model";

export const create = async (dto, session) => new Payment(dto).save({ session });

export const update = async (_id, dto, options = {}) => {
    return Payment.findByIdAndUpdate(_id, dto, { new: true, ...options });
};

export const getOne = async (filter, options = {}) => {
    return Payment.findOne(filter, {}, { lean: true, ...options });
};

export const getMany = async (dto, options = {}) => {
    const { limit, page, ...queryParams } = dto;
    const filter = composeFilter(queryParams);
    return options.dontPaginate
        ? Payment.find(filter, {}, { lean: true, ...options })
        : paginate(Payment.modelName, { filter, limit, page, ...options });
};
