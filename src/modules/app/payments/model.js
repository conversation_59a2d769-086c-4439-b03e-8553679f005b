import { model, Schema } from "mongoose";
import aggregatePaginator from "mongoose-aggregate-paginate-v2";
import paginator from "mongoose-paginate-v2";
import { PAYMENT_METHOD } from "./constant";
import { idNameSchema } from "helpers/constants";

const schema = new Schema(
    {
        organization: { type: "String", required: true, ref: "Organization" }, // organizationId
        reference: { type: "String", required: true },
        amount: { type: "Number", required: true },
        currency: { type: "String", required: true },
        reason: { type: "String", required: true },
        staffCount: { type: "Number" },
        patientsCount: { type: "Number" },
        patients: [{ type: "String" }],
        status: { type: "String", required: true },
        subscriptionId: { type: "String" },
        retries: { type: "Number", required: true, default: 0 },
        method: { type: "String", enum: Object.values(PAYMENT_METHOD) },
        operator: idNameSchema,
        items: [
            new Schema({
                description: { type: "String", required: true },
                amount: { type: "Number", required: true }
            })
        ]
    },
    { timestamps: true }
);

schema.index({ organization: 1, reference: 1 });
schema.plugin(paginator);
schema.plugin(aggregatePaginator);
const Model = model("Payment", schema);
export default Model;
