import { isAuthorized } from "base/request";
import { Router } from "express";
import { validator } from "helpers/config";
import { setMaximumTime } from "pharmacy/prescriptions/request";
import { fetch, initializePayment, retry, topUpCredits, fetchTopupSummary, webhook } from "app/payments/controller";
import {
    fetchPaymentsSchema,
    initializePaymentSchema,
    retrySchema,
    topUpSchema,
    validatePaymentInit,
} from "app/payments/request";

const router = Router();

router.post("/payments/initialize", validator.body(initializePaymentSchema), validatePaymentInit, initializePayment);

router.post("/payments/webhook", webhook);
router.get("/payments", isAuthorized(["super-view"]), validator.body(fetchPaymentsSchema), setMaximumTime, fetch);
router.get("/payments/topup-summary", isAuthorized(["super-view"]), fetchTopupSummary);
router.post("/payments/:_id/retry", validator.query(retrySchema), retry);
router.post("/payments/credits-topup", validator.body(topUpSchema), topUpCredits);

export default router;
