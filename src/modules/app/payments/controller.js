/* eslint-disable camelcase */
import { JOB_EXECUTION_TIME, paymentTypes } from "helpers/constants";
import { error, success } from "helpers/response";
import { charge, initTransaction } from "services/pay";
import { compileTopUpDocs, logPayment, retryEMRBilling, sendCheckOutMail } from "app/payments/helper";
import * as PaymentService from "app/payments/service";
import OrganizationService from "settings/organizations/service";
import agenda from "config/agenda";
import JOB from "constants/jobs";
import { createHmac } from "crypto";
import { PAYMENT_METHOD } from "./constant";

export const initializePayment = async (req, res) => {
    try {
        const { email, metadata, amount, currency } = res.locals.record;
        const paymentDetails = { email, metadata, amount, status: "pending", currency, method: PAYMENT_METHOD.AGENT };
        const { organization, reason } = metadata;

        const response = await initTransaction({ ...paymentDetails });
        if (typeof response === "string") {
            await logPayment({ ...paymentDetails, status: "failed" });
            return error(res, 500, `Paystack Error ${response}. Please check your email to view more`);
        }

        if (reason === paymentTypes.EMRSubscription) {
            const org = await OrganizationService.update(organization, {
                checkOutLink: response.authorization_url
            });
            sendCheckOutMail(org, response);
        }

        return success(res, 200, response);
    } catch (err) {
        return error(res, 500, err);
    }
};

/**
 * @description Retries a failed payment
 * @param {*} req Query: _type_
 * @param {*} res
 * @returns
 */
export const retry = async (req, res) => {
    try {
        switch (req.query.type) {
            case paymentTypes.EMRSubscription:
                return retryEMRBilling(req, res);
            default:
                return success(res, 200, `Indicate Payment Type`);
        }
    } catch (err) {
        return error(res, 500, err);
    }
};

export const topUpCredits = async (req, res) => {
    try {
        const { parentOrganizationId, profile } = req.user.currentLocation;
        const payload = await compileTopUpDocs({
            parentOrganizationId,
            amount: req.body.amount,
            operator: profile
        });
        const response = await charge(payload);
        if (typeof response === "string") {
            await logPayment({ ...payload, status: "failed" });
            return error(res, 500, `Paystack Error: ${response}`);
        }
        await agenda.schedule(JOB_EXECUTION_TIME, JOB.DEDUCT_PENDING_TRANSACTION, { parentOrganizationId });
        await logPayment(payload);
        return success(res, 200, response);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const webhook = async (req, res) => {
    try {
        const hash = createHmac("sha512", process.env.PAYSTACK_SECRET).update(JSON.stringify(req.body)).digest("hex");

        if (hash !== req.headers["x-paystack-signature"]) {
            return error(res, 403, `unauthorized`);
        }

        if (req.body.event === "charge.success") {
            agenda.schedule(JOB_EXECUTION_TIME, JOB.PROCESS_PAYMENT, { ...req.body.data });
        }
        return success(res, 200, "OK");
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetch = async (req, res) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        const populate = [{ path: "organization", select: "name currency address" }];
        const payments = await PaymentService.getMany(
            {
                organization: parentOrganizationId,
                ...req.query
            },
            { dontPaginate: false, updatedAt: -1, populate }
        );
        return success(res, 200, payments);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetchTopupSummary = async (req, res) => {
    const { parentOrganizationId } = req.user.currentLocation;
    try {
        const filter = { organization: parentOrganizationId };
        const pipeline = [
            {
                $match: filter
            },
            {
                $group: {
                    _id: "$organization",
                    totalAmount: { $sum: "$amount" }
                }
            }
        ];
        const payments = await PaymentService.getMany(filter, { dontPaginate: false, updatedAt: -1, pipeline });

        return success(res, 200, { totalPayment: payments.docs[0].totalAmount });
    } catch (err) {
        return error(res, 500, err);
    }
};
