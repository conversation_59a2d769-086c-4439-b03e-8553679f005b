# Payments

Contains all endpoints regarding organizations' payments
Documentation will be captured with respect to the routes

## Things To Note

Paystack is the payment processor currently in use. The account is owned by Summitech.<br>
API keys are gotten from the company's Paystack dashboard.<br>
For production, the API keys, webhook and call back endpoints need to be specified by an admin.<br>
Service initialization can be found [here](../../../services/pay.js)

## [Routes](./route.js)

### `initializePayment` (/payments/initialize)

-   description: initializes a payment <br>
-   parameters: <br>
    -   `email`: email address of the payer <br>
    -   `amount`: amount to be paid (in the currencies least division e.g. Kobo for Naira, Cents for USD) <br>
    -   `metadata`: other necessary details needed for capture.
-   procedure (controller):<br>
    -   payment is initialized with the `initTransation` function as defined in the services file [here](../../../services/pay.js)
    -   a failed response from the payment processor sets the payment status to _failed_
    -   if the response is successful, and the reason for the payment (as described in the metadata) is _EMR Subscription_, the checkout link from paystack is saved in the organization's document. This is to ensure lost authorization urls are retrievable.<br>
-   returns: <br>
    -   paystack's response, including an `authorization_url` as the chack out link(to make payments)

### `webhook` (/payments/webhook)

-   desciption: receives events recorded by paystack. Focus is on the `charge.success` event. <br>
-   validation:<br>
    -   Since we're working with paystack, the verification is done based on paystack's recommendation - comparing the header `x-paystack-signature` to Summitech's hashed secret key. [verifyWebhook](./request.js).
    -   Every event aside the `charge.success` event receives a `200` response. <br>
-   procedure: <br>
    -   Each response is handled with respect to the `reason` parameter in the payload's `metadata`. <br>
    -   all payments are logged in the Payments collection.<br>
    -   The below are types of payments currently handled:
        -   `handleEMRSubscription`: currently not in use
        -   `changePaymentMethod`: records card details when a new card is created or changed
        -   `handleCreditTopUp`: records credits purchase by each organization
        -   `handleLicensePurchase`: records staff licenses purchases by each organization in the organization's payload
        -   `handleStaffSubscription`: after an organization's staff licenses are payed, this function handles updating the renewal dates for these staff, and last subscription date for the organization.

### `topUpCredits` (/payments/credits-topup)

-   description: purchase patients credits in an organization. 1 credit unit = 1 Naira.
-   parameters: <br>
    -   `amount`: amount of units to be purchased (in currency's least division)
-   procedure:
    -   a card has to be active for the organization
    -   payment is logged with the `pending` status. Status changes with respect to the status received from paystack's `initTransaction` method.
    -   request is handled by the `handleCreditTopUp` hook.

### purchaseLicenses (/payments/licenses-topup)

-   similar to `topUpCredits`, except here, staff licenses are being purchased.
-   parameters:
    -   `quantity`: number of licenses to be purchased <br>
-   validation:
    -   a card has to be available for this organization
    -   subscripton has to be active fo the organization
    -   purchaseable licenses depends on the plan's maximum staff count. Ideally, noOfLicenses <= plan.maxStaffCount
