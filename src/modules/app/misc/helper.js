import {
    customLabels,
    customOptions,
    deactivatableModels,
    deletableModels,
    operationTypes
} from "admin/audit-trail/constants";
import { compareFn, removeElementsFromArray } from "helpers/utils";
import * as inflection from "inflection";
import { model, modelNames } from "mongoose";

const getApplicableOperationTypes = (modelName) => {
    let types = Object.keys(operationTypes);
    if (!deletableModels.includes(modelName)) types = removeElementsFromArray(types, ["delete"]);
    if (!deactivatableModels.includes(modelName)) types = removeElementsFromArray(types, ["deactivate", "reactivate"]);
    return types;
};

const getCustomLabel = (collection, operationType) => {
    const option = customLabels.find((obj) => {
        return obj["data-collection"] === collection && obj["data-operationType"] === operationType;
    });
    return option?.label;
};

export const generateDropDownOptionsPerModel = (modelName) => {
    const collection = model(modelName).collection.collectionName;
    const entity = inflection.underscore(modelName).replace(/_/g, " ");
    const opTypes = getApplicableOperationTypes(modelName);
    return opTypes.map((operationType) => {
        const action = operationTypes[operationType];
        const label = getCustomLabel(collection, operationType) || `${action} ${entity}`;
        const value = `${action}-${collection}`;
        return { label, value, "data-collection": collection, "data-operationType": operationType };
    });
};

const collectionsToExclude = ["AuditTrail", "FailedLog", "Currency", "DailyStatistics", "Notification"];

export const getBaseOptionsForAuditTrail = () => {
    return modelNames()
        .filter((name) => !collectionsToExclude.includes(name))
        .map((name) => generateDropDownOptionsPerModel(name))
        .flat();
};

export const getDropdownOptionsForAuditTrail = () => {
    return getBaseOptionsForAuditTrail().concat(customOptions).sort(compareFn("data-collection"));
};
