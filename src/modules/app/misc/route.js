import {
    addPermission,
    addSystemValues,
    fetchAnaestheticComplications,
    fetchCurrencies,
    fetchDropdownOptions,
    fetchPermissions,
    sendEMail
} from "app/misc/controller";
import { sendEmailSchema } from "app/misc/request";
import { isAuthorized } from "base/request";
import { Router } from "express";
import { validator } from "helpers/config";
import Jo<PERSON> from "joi";

const addPermissionSchema = Joi.object({
    title: Joi.string().required(),
    display_title: Joi.string().required()
}).required();

const router = Router();
router.get("/permissions", fetchPermissions);
router.post("/permissions", validator.body(addPermissionSchema), addPermission);
router.get("/currencies", fetchCurrencies);
router.get("/currencies", fetchCurrencies);
router.get("/anaesthesia-complications", fetchAnaestheticComplications);
router.post("/send-mail", isAuthorized([]), validator.body(sendEmailSchema), sendEMail);
router.get("/dropdown-options", fetchDropdownOptions);
router.post("/system-values", addSystemValues);
export default router;
