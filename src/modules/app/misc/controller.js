import * as dropdownOptions from "app/misc/constants";
import { getDropdownOptionsForAuditTrail } from "app/misc/helper";
import { AnaesthesiaComplication, Currency, Permission } from "app/misc/model";
import agenda from "config/agenda";
import JOB from "constants/jobs";
import { JOB_EXECUTION_TIME } from "helpers/constants";
import { error, success } from "helpers/response";
import { model } from "mongoose";

export const fetchPermissions = async (req, res) => {
    try {
        const data = await Permission.find({}).lean();
        return success(res, 200, data);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const addPermission = async (req, res) => {
    try {
        const { title } = req.body;
        const result = await Permission.findOneAndUpdate({ title }, { ...req.body }, { new: true, upsert: true });
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetchCurrencies = async (req, res) => {
    try {
        const data = await Currency.find({}).lean();
        return success(res, 200, data);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const sendEMail = async (req, res) => {
    try {
        await agenda.schedule(JOB_EXECUTION_TIME, JOB.EMAIL, { ...req.body, template: req.body.body });
        return success(res, 200);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetchAnaestheticComplications = async (req, res) => {
    try {
        const data = await AnaesthesiaComplication.find({}).lean();
        return success(res, 200, data);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetchDropdownOptions = async (req, res) => {
    try {
        const options = { ...dropdownOptions, actions: getDropdownOptionsForAuditTrail() };
        return success(res, 200, options);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const addSystemValues = async (req, res) => {
    try {
        const { modelName, values, specialties } = req.body;
        const docs = values
            .split(",")
            .map((value) => ({ value: value.trim(), specialties }))
            .filter((obj) => obj.value !== "");
        const records = await model(modelName).insertMany(docs);
        return success(res, 200, records);
    } catch (err) {
        return error(res, 500, err);
    }
};
