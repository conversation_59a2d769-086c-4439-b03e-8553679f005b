/** @namespace CaseNoteController */
import { addToCart } from "cart/controller";
import {
    annotateUpdatesToCasenote,
    composeDoc,
    composeOrderDoc,
    composeProcedureAlertData,
    composeProcedureDoc,
    extractCaseNoteRecord,
    getPatientHistory,
    internalLabProcedureAlert,
} from "clinic/case-notes/helpers";
import CaseNoteService from "clinic/case-notes/service";
import AppointmentService from "frontdesk/appointments/service";
import PatientService from "frontdesk/patients/service";
import { error, success } from "helpers/response";
import { addHoursToDate } from "helpers/utils";
import Task from "labs/tasks/model";
import Mongoose from "mongoose";
import Procedure from "outpatient/procedures/model";
import { composeSurgeryDoc } from "outpatient/surgery/helper";
import Surgery from "outpatient/surgery/model";
import { uploadFiles } from "services/storage";
import throwErrorWithCode from "utils/throw-error";

const {
    Types: { ObjectId },
    startSession
} = Mongoose;

/**
 * Fetches case notes
 * @async
 * @function getMany
 * @param {Object} req
 * @param {Object} res
 * @returns Promise<Object>
 * @see {@link http://mongoosejs.com/docs/api.html#query_Query-find
 */
export const getMany = async (req, res) => {
    const { parentOrganizationId } = req.user.currentLocation;
    try {
        const casenotes = await CaseNoteService.getMany({ ...req.query, parentOrganizationId }, { dontPaginate: true });
        return success(res, 200, casenotes);
    } catch (err) {
        return error(res, 500, err);
    }
};

/**
 * Creates Case Note
 * @async
 * @function create
 * @param {Object} req
 * @param {Object} res
 * @returns Promise<Object>
 * @see {@link http://mongoosejs.com/docs/api.html#query_Query-create
 */
export const create = async (req, res) => {
    try {
        const { profile: operator, _id: branchId, parentOrganizationId } = req.user.currentLocation;

        const doc = await composeDoc(req, res);
        let caseNote;

        const session = await startSession();
        await session.withTransaction(async () => {
            caseNote = await CaseNoteService.create(doc, session);
            const patientHistory = getPatientHistory(caseNote);
            await PatientService.update(
                caseNote.patient._id,
                { $addToSet: { pastMedicalHistory: { $each: patientHistory } } },
                session
            );
        });
        await session.endSession();

        await extractCaseNoteRecord(
            req.body,
            null,
            "insert",
            { operator, parentOrganizationId, branchId, documentId: caseNote._id },
            { ...caseNote }
        );

        return success(res, 200, caseNote);
    } catch (err) {
        return error(res, 500, err);
    }
};

/**
 * Update Case Note
 * @async
 * @function update
 * @param {Object} req
 * @param {Object} res
 * @returns Promise<Object>
 * @see {@link http://mongoosejs.com/docs/api.html#query_Query-update
 */
export const update = async (req, res) => {
    const { caseNote } = res.locals;
    try {
        const { profile: operator, _id: branchId, parentOrganizationId } = req.user.currentLocation;

        if ((req.body.attachments || []).length && typeof req.body.attachments[0] === "object") {
            req.body.attachments = await uploadFiles(req.body.attachments, "casenotes");
        }

        const annotatedDoc = annotateUpdatesToCasenote(req, res);
        let ucaseNote;
        const session = await startSession();
        await session.withTransaction(async () => {
            ucaseNote = await CaseNoteService.update(req.params._id, { ...annotatedDoc, updatedBy: operator }, session);
            const newPatientHistory = getPatientHistory(ucaseNote);
            const existingPatientHistory = getPatientHistory(caseNote);

            const uniqueNewHistory = newPatientHistory.filter(newItem =>
                !existingPatientHistory.some(existingItem =>
                    existingItem.name === newItem.name &&
                    existingItem.type === newItem.type &&
                    existingItem.sourceField === newItem.sourceField
                )
            );

            if (uniqueNewHistory.length > 0) {
                await PatientService.update(
                    ucaseNote.patient._id,
                    { $addToSet: { pastMedicalHistory: { $each: uniqueNewHistory } } },
                    session
                );
            }
        });
        await extractCaseNoteRecord(
            req.body,
            caseNote,
            "update",
            { operator, parentOrganizationId, branchId, documentId: caseNote._id },
            { ...ucaseNote }
        );
        return success(res, 200, ucaseNote);
    } catch (err) {
        return error(res, 500, err);
    }
};

/**
 * Archive Case Note
 * @async
 * @function archive
 * @param {Object} req
 * @param {Object} res
 * @returns Promise<Object>
 * @see {@link http://mongoosejs.com/docs/api.html#query_Query-update
 */
export const archive = async (req, res) => {
    try {
        const { parentOrganizationId, profile: operator } = req.user.currentLocation;
        const appointment = await AppointmentService.getOne({
            _id: new ObjectId(req.body.appointmentId),
            parentOrganizationId
        });

        if (appointment) {
            const caseNote = await CaseNoteService.update(req.params._id, {
                isArchived: true,
                reasonToArchive: req.body.reason,
                operator
            });
            return success(res, 200, caseNote);
        }
        return error(res, 400, "Appointment Expired Can't Archive CaseNote");
    } catch (err) {
        return error(res, 500, err);
    }
};

/**
 * Save recommended plan (admission) & update Px object with plan info
 * @async
 * @function saveAdmission
 * @param {Object} req
 * @param {Object} res
 * @returns Promise<Object>
 * @see {@link http://mongoosejs.com/docs/api.html#query_Query-update
 */
export const saveAdmission = async (req, res) => {
    const { parentOrganizationId } = req.user.currentLocation;
    try {
        const appointment = await AppointmentService.getOne({
            _id: new ObjectId(req.body.appointmentId),
            hasAppointmentEnded: false,
            parentOrganizationId
        });

        if (appointment) {
            const session = await startSession();
            await session.withTransaction(async () => {
                const patientAdmission = {
                    doctor: {
                        _id: appointment.consultant._id,
                        name: appointment.consultant.name
                    },
                    date: req.body.startDate,
                    diagnosis: req.body.diagnosis,
                    reason: req.body.reason,
                    specialInstruction: req.body.specialInstruction,
                    branchId: appointment.branchId,
                    endDate: req.body.endDate
                };
                await CaseNoteService.update(req.params._id, { admission: patientAdmission }, session);
                await PatientService.update(appointment.patient._id, { admission: patientAdmission }, session);
            });
            await session.endSession();
            return success(res, 200, appointment);
        }
        return error(res, 400, "Appointment Ended Can't Add Admission");
    } catch (err) {
        return error(res, 500, err);
    }
};

/**
 * Save recommended plan (procedure)
 * @async
 * @function saveProcedure
 * @param {Object} req
 * @param {Object} res
 * @returns Promise<Object>
 * @see {@link http://mongoosejs.com/docs/api.html#query_Query-update
 */
export const saveProcedure = async (req, res) => {
    const { profile: operator, parentOrganizationId } = req.user.currentLocation;
    try {
        let bill;
        let result;
        const { admission, appointment, procedure } = res.locals;

        let procedureDoc = composeProcedureDoc(req, admission, appointment);

        const session = await startSession();
        await session.withTransaction(async () => {
            if (req.body.attachments) {
                req.body.attachments = await uploadFiles(req.body.attachments, "procedure-attachements");
            }
            if (req.body.procedureType === "internalLab" || req.body.procedureType === "inOffice") {
                const labDoc = await composeOrderDoc(req);
                const task = await new Task(labDoc).save({ session });
                procedureDoc = { ...procedureDoc, task: task._id };
                const billPayload = res.locals.bill;
                billPayload.patient = admission ? admission.patient : appointment.patient;
                bill = await addToCart(billPayload, session);
            }
            if (!procedure) {
                result = await new Procedure(procedureDoc).save({ session });
            } else {
                result = await Procedure.findByIdAndUpdate(
                    { _id: procedure._id },
                    { $set: { ...procedureDoc } },
                    { session }
                );
            }
        });
        await session.endSession();

        if (bill && appointment?.appointmentStateHistory) {
            const newNotification = {
                title: "Patient Check In",
                body: `${operator.name} has transferred ${req.body.patient.name} for Procedure`,
                topic: operator._id,
                parentOrganizationId: parentOrganizationId,
                section: "laboratory",
                expiryDate: addHoursToDate(new Date(), 12)
            };
            const alertData = composeProcedureAlertData(appointment, admission);
            internalLabProcedureAlert(req, alertData, newNotification);
        }
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

/**
 * Save recommended plan (surgery)
 * @async
 * @function bookSurgery
 * @param {Object} req
 * @param {Object} res
 * @returns Promise<Object>
 * @see {@link http://mongoosejs.com/docs/api.html#query_Query-update
 */
export const bookSurgery = async (req, res) => {
    try {
        let result;
        const doc = await composeSurgeryDoc(req, res);
        const session = await startSession();
        await session.withTransaction(async () => {
            const billPayload = res.locals.bill;
            if (billPayload) await addToCart(billPayload, session);
            result = await Surgery.create([{ ...doc }], { session });
        });
        await session.endSession();
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

const treatmentPlans = {
    MedicalReferral: "Referral",
    RxPrescription: "Prescription",
    Appointment: "Follow-up Appointment"
};
/**
 *
 * @param {string} casenoteId
 * @param {object} doc
 * @param {string} doc._id
 * @param {string} doc.model
 * @param {*} session
 */
export const addTreatmentPlan = async (casenoteId, doc, session) => {
    if (!casenoteId || !doc || !doc._id || !doc.model) throwErrorWithCode("Problem adding treatment plan.", 500);
    const treatmentPlan = { doc: doc._id, docModel: doc.model, title: treatmentPlans[doc.model] || doc.model };
    await CaseNoteService.update(casenoteId, { $push: { treatmentPlans: treatmentPlan } }, session);
};

export const addIdToComplaintsWithoutId = async (req, res) => {
    try {
        const affectedCasenotes = await CaseNoteService.getMany(
            {
                specialty: "ophthalmology",
                complaint: { $elemMatch: { _id: { $exists: false } } },
                parentOrganizationId: { $exists: true }
            },
            { dontPaginate: true }
        );
        const writes = affectedCasenotes.map((casenote) => {
            const updatedComplaints = casenote.complaint.map((complaint) => ({ ...complaint, _id: new ObjectId() }));
            return {
                updateOne: {
                    filter: { _id: casenote._id },
                    update: { $set: { complaint: updatedComplaints } }
                }
            };
        });
        await CaseNoteService.bulkWrite(writes);
        return success(res, 200, writes);
    } catch (err) {
        return error(res, 500, err);
    }
};
