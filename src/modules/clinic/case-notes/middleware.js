import {
    composeSurgeryPayload,
    hasExceededGracePeriod,
    labPayload,
    procedurePayload,
    validateFundus,
    validateRefraction,
    validateVisualAcuity
} from "clinic/case-notes/helpers";
import { isAfter } from "date-fns";
import Appointment from "frontdesk/appointments/model";
import { appointmentStateConst, arrayOfIdNameRequestSchema, specialtyRequest } from "helpers/constants";
import { error } from "helpers/response";
import { isEnv } from "helpers/utils";
import Admission from "inpatient/admissions/model";
import Joi from "joi";
import { Types } from "mongoose";
import { bookSurgeryStatus } from "outpatient/surgery/helper";
import { bookSurgerySchema, confirmTheatreAvailability, idNameSchema, signature } from "outpatient/surgery/request";

import CaseNote from "clinic/case-notes/model";
import { APPOINTMENT_STATUS, populateConsultant } from "frontdesk/appointments/constants";
import AppointmentService from "frontdesk/appointments/service";
import CaseNoteService from "./service";

const { ObjectId } = Types;

const authorSchema = Joi.object({
    _id: Joi.string().required(),
    name: Joi.string().required()
});
const metadataSchema = {
    createdBy: authorSchema,
    updatedBy: authorSchema,
    createdAt: Joi.date(),
    updatedAt: Joi.date()
};

export const medicalHistoryRequestSchema = {
    personalHistory: Joi.object({
        medicalConditions: Joi.array().items(Joi.object({ ...idNameSchema })),
        genotype: Joi.string().valid("AA", "AS", "SS", "SC", "AC"),
        bloodGroup: Joi.string().valid("A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"),
        bloodTransfusion: Joi.string(),
        numberOfTimesBloodTransFusion: Joi.number(),
        lastDateBloodTransfusion: Joi.date(),
        comment: Joi.string().allow(""),
        summary: Joi.string()
    }),
    allergies: Joi.object().keys({
        otherAllergies: Joi.string().allow(""),
        drugName: Joi.array().items(
            Joi.object({
                _id: Joi.string().required(),
                name: Joi.string().required(),
                duration: Joi.string()
            })
        ),
        summary: Joi.string()
    }),
    familySocialHistory: Joi.object().keys({
        selectedDiseases: Joi.array().items(Joi.object({ ...idNameSchema})),
        others: Joi.string().allow(""),
        summary: Joi.string()
    }),
    assessment: Joi.object({
        assessment: Joi.array().items(Joi.string()),
        others: Joi.string().allow(""),
        summary: Joi.string()
    }),
    pastSurgeries: Joi.object({
        surgeries: Joi.array().items(
            Joi.object({
                name: Joi.string().required(),
                time: Joi.date()
            })
        ),
        others: Joi.string().allow(""),
        summary: Joi.string()
    }),
    ...metadataSchema
};

export const rightLeftEyeSchema = {
    rightEye: Joi.string(),
    leftEye: Joi.string()
};

export const rightLeftEyeIdNameSchema = {
    rightEye: idNameSchema,
    leftEye: idNameSchema
};

const visualAcuitySchema = {
    sh: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
    ph: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
    cc: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
    near: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
    bv: Joi.object({ ...rightLeftEyeSchema }),
    cv: Joi.object({ ...rightLeftEyeSchema }),
    eh: Joi.object({ ...rightLeftEyeSchema }),
    pv: Joi.object({ ...rightLeftEyeSchema }),
    ea: Joi.object({ ...rightLeftEyeSchema }),
    llv: Joi.object({ ...rightLeftEyeSchema }),
    iop: Joi.array().items(Joi.object({ ...rightLeftEyeSchema })),
    iopMachine: Joi.string()
};

const codeSchema = Joi.object().keys({
    _id: Joi.string().required(),
    code: Joi.string(),
    name: Joi.string().required(),
    ...metadataSchema
});

const refractionSchema = {
    subjective: Joi.object({
        dv: Joi.object({
            sph: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
            cyl: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
            ax: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
            add: Joi.array().items(Joi.object({ ...rightLeftEyeSchema })),
            va: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
            vaCorrection: Joi.object({ ...rightLeftEyeSchema })
        }),
        nv: Joi.object({
            sph: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
            cyl: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
            ax: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
            add: Joi.array().items(Joi.object({ ...rightLeftEyeSchema })),
            va: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
            vaRX: rightLeftEyeSchema
        })
    }),
    habitualRx: Joi.object({
        spherical: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        cyl: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        axis: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        add: Joi.array().items(Joi.object({ ...rightLeftEyeSchema })),
        vaHabitualCorrection: rightLeftEyeSchema
    }),
    finalRx: Joi.object({
        spherical: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        cyl: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        axis: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        add: Joi.array().items(Joi.object({ ...rightLeftEyeSchema })),
        va: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        vaRX: rightLeftEyeSchema
    }),
    refractionCyclo: Joi.object({
        sph: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        cyl: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        ax: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        refraction: Joi.array().items(Joi.object({ ...rightLeftEyeSchema }))
    }),
    postMydriaticTest: Joi.object({
        sph: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        cyl: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        ax: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        refraction: Joi.array().items(Joi.object({ ...rightLeftEyeSchema }))
    }),
    retinoscopy: Joi.object({
        sph: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        cyl: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        ax: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        refraction: Joi.array().items(Joi.object({ ...rightLeftEyeSchema }))
    }),
    keratometry: Joi.object({
        k1: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        k2: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        axis: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema })),
        astigmatism: Joi.array().items(Joi.object({ ...rightLeftEyeIdNameSchema }))
    }),
    ...metadataSchema
};

const followUpRequestSchema = {
    lastAppointmentDate: Joi.date(),
    surgeryDate: Joi.date(),
    selectedEye: Joi.string(),
    surgicalResultFeedback: Joi.string(),
    postOpVisit: Joi.object({
        od: Joi.object({
            value: Joi.number(),
            calendar: Joi.date()
        }),
        os: Joi.object({
            value: Joi.number(),
            calendar: Joi.date()
        })
    }),
    visualAcuity: Joi.object({ ...visualAcuitySchema, ...metadataSchema }),
    refraction: Joi.object({
        ...refractionSchema,
        comments: Joi.string()
    }),
    extraOcularMusclesMovement: Joi.object({
        ...rightLeftEyeSchema,
        iop: Joi.object({ ...rightLeftEyeSchema }),
        adjusted: Joi.object({ ...rightLeftEyeSchema }),
        pupils: Joi.object({ ...rightLeftEyeSchema }),
        orbit: Joi.object({ ...rightLeftEyeSchema }),
        lids: Joi.object({ ...rightLeftEyeSchema }),
        conjunctiva: Joi.object({ ...rightLeftEyeSchema }),
        cornea: Joi.object({ ...rightLeftEyeSchema }),
        acIris: Joi.object({ ...rightLeftEyeSchema }),
        lens: Joi.object({ ...rightLeftEyeSchema }),
        vitreous: Joi.object({ ...rightLeftEyeSchema })
    }),
    fundus: Joi.object({
        ...rightLeftEyeIdNameSchema,
        disk: Joi.object({ ...rightLeftEyeSchema }),
        vessels: Joi.object({ ...rightLeftEyeSchema }),
        macular: Joi.object({ ...rightLeftEyeSchema }),
        periphery: Joi.object({ ...rightLeftEyeSchema }),
        ...metadataSchema
    }),
    diagnosis: codeSchema,
    otherDiagnosis: codeSchema,
    procedure: Joi.object({ ...idNameSchema }),
    treatmentPlan: Joi.string(),
    nextAppointmentDate: Joi.date(),
    comment: Joi.string(),
    specialRequest: Joi.string(),
    physician: Joi.object({ ...idNameSchema }),
    causeOfPoorVisualOutcome: Joi.object({
        followUpReson: Joi.string(),
        followUpRemark: Joi.string()
    }),
    surgeryDone: Joi.object({ ...idNameSchema }),
    attachments: Joi.any(),
    signature: Joi.object({ ...signature }),
    ...metadataSchema
};

const ophthalmologyComplaintEyeSchema = {
    _id: Joi.string(),
    site: Joi.string().required().valid("rightEye", "leftEye", "unspecified"),
    howLong: Joi.string(),
    describeComplaint: Joi.array().items(Joi.string()),
    severity: Joi.string(),
    degreeOfPain: Joi.string(),
    symptoms: Joi.array().items(
        Joi.object().keys({
            _id: Joi.string().required(),
            code: Joi.string(),
            name: Joi.string().required(),
            howLong: Joi.string(),
            describeComplaint: Joi.array().items(Joi.string()),
            severity: Joi.string(),
            gettingBetter: Joi.boolean(),
            gettingWorse: Joi.boolean(),
            additionalComment: Joi.string()
        })
    ),
    historyOfPresentEye: Joi.string(),
    previousEyeProblems: Joi.string(),
    recommendations: Joi.string(),
    gettingBetter: Joi.boolean(),
    gettingWorse: Joi.boolean(),
    otherComments: Joi.string().when("severity", { is: null, then: Joi.required() }),
    additionalComment: Joi.string(),
    summary: Joi.string(),
    haveYouSortAdviceBefore: Joi.boolean()
};

const generalPracticeComplaint = Joi.array().items(
    Joi.object().keys({
        radiateTo: Joi.string(),
        keepUsAtNight: Joi.boolean(),
        interferWithMyDailyActivities: Joi.boolean(),
        hasHappenedBefore: Joi.boolean(),
        stayingTheSame: Joi.boolean(),
        locatedAt: Joi.string(),
        howLong: Joi.string(),
        therapaueticManoeuversWorse: Joi.string(),
        therapaueticManoeuversBetter: Joi.string(),
        haveYouMentionedBefore: Joi.boolean(),
        aggravatedBy: Joi.string(),
        symptoms: Joi.array().items(
            Joi.object().keys({
                _id: Joi.string().required(),
                code: Joi.string(),
                name: Joi.string().required(),
                howLong: Joi.string(),
                describeComplaint: Joi.array().items(Joi.string()),
                severity: Joi.string(),
                gettingBetter: Joi.boolean(),
                gettingWorse: Joi.boolean(),
                additionalComment: Joi.string()
            })
        ),
        comments: Joi.string(),
        relievedBy: Joi.string(),
        expiryDate: Joi.string(),
        summary: Joi.string().required(),
        ...metadataSchema
    })
);

const ophthalmologyComplaint = Joi.array().items(Joi.object({ ...ophthalmologyComplaintEyeSchema, ...metadataSchema }));

const generalPracticeSystemReview = Joi.object({
    respiratory: Joi.string(),
    ent: Joi.string(),
    gastrointestinal: Joi.string(),
    centralnervous: Joi.string(),
    muscloskeletal: Joi.string(),
    pr: Joi.string(),
    cardiovascular: Joi.string(),
    urogental: Joi.string(),
    ve: Joi.string(),
    breast: Joi.string(),
    comment: Joi.string(),
    summary: Joi.string(),
    ...metadataSchema
});

const ophthalmologySystemReview = Joi.object({
    constitutional: Joi.object({
        name: Joi.array().items(Joi.string()),
        cancerName: Joi.string().allow(null, "")
    }),
    respiratory: Joi.string(),
    ent: Joi.string(),
    gastrointestinal: Joi.string(),
    neurological: Joi.string(),
    musculoskeletal: Joi.string(),
    psychological: Joi.string(),
    cardiovascular: Joi.string(),
    genitourinary: Joi.string(),
    dermatological: Joi.string(),
    endocrine: Joi.string(),
    haematologicLymphatic: Joi.string(),
    allergyImmune: Joi.string(),
    comment: Joi.string().allow(null, ""),
    summaryText: Joi.string(),
    ...metadataSchema
});

export const caseNoteRequestSchema = Joi.object({
    appointmentId: Joi.string().required(),
    selectedEye: Joi.string(),
    complaint: Joi.alternatives(generalPracticeComplaint, ophthalmologyComplaint).required().messages({
        "any.required": "Complaint is required"
    }),
    systemReview: Joi.alternatives(generalPracticeSystemReview, ophthalmologySystemReview),
    medicalHistory: Joi.object().keys({
        ...medicalHistoryRequestSchema
    }),
    rightEyeDiagnosis: Joi.array().items(codeSchema),
    leftEyeDiagnosis: Joi.array().items(codeSchema),
    rightEyeOtherDiagnosis: Joi.array().items(codeSchema),
    leftEyeOtherDiagnosis: Joi.array().items(codeSchema),
    refraction: Joi.array().items(
        Joi.object({
            ...refractionSchema,
            otherComments: Joi.string(),
            attachment: Joi.any(),
            summaryText: Joi.string(),
            ...metadataSchema
        })
    ),
    visualAcuity: Joi.array().items(
        Joi.object({
            ...visualAcuitySchema,
            comment: Joi.string(),
            summaryText: Joi.string(),
            ...metadataSchema
        })
    ),
    anteriorSegmentExam: Joi.object({
        iop: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        iopDate: Joi.object({
            rightEye: Joi.date(),
            leftEye: Joi.date()
        }),
        iopMachine: Joi.string(),
        genoscopy: Joi.object({
            rightEye: Joi.string(),
            leftEye: Joi.string()
        }),
        adjustedIop: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        lids: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        conjunctiva: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        sclera: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        orbit: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        cornea: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        pupil: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        ac: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        iris: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        lens: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        comment: Joi.string(),
        summaryText: Joi.string(),
        ...metadataSchema
    }),
    physicalExamination: Joi.object().keys({
        nourishment: Joi.string(),
        development: Joi.string(),
        mentalState: Joi.string(),
        disease: Joi.string(),
        temprature: Joi.string(),
        hydration: Joi.string(),
        colour: Joi.object({
            cyanosis: Joi.string(),
            pallor: Joi.string(),
            pigmentation: Joi.string(),
            jaundice: Joi.string()
        }),
        lymphNodes: Joi.object({
            preauricular: Joi.string(),
            postAuricular: Joi.string(),
            submandibular: Joi.string(),
            cervical: Joi.string(),
            supraclavicular: Joi.string(),
            axillary: Joi.string(),
            inguinal: Joi.string(),
            popliteal: Joi.string()
        }),
        edema: Joi.object({
            feet: Joi.string(),
            hand: Joi.string(),
            face: Joi.string()
        }),
        otherSymptoms: Joi.string(),
        summary: Joi.string().required()
    }),
    diagnosis: Joi.array().items(codeSchema),
    systemicConditions: arrayOfIdNameRequestSchema,
    otherDiagnosis: Joi.array().items(codeSchema),
    attachments: Joi.array(),
    narrative: Joi.string(),
    review: Joi.string(),
    ...specialtyRequest(),
    signedBy: Joi.object({
        name: Joi.string().required(),
        time: Joi.date().required()
    }),
    pastOcularHistory: Joi.object({
        lastEyeExamDate: Joi.date(),
        doYouWearGlasses: Joi.boolean(),
        doYouWearContactLens: Joi.boolean(),
        howLongHaveYouWornGlasses: Joi.string(),
        howLongHaveYouWornContactLens: Joi.string(),
        contactLensBrand: Joi.string(),
        howOldIsYourCurrentPair: Joi.string(),
        visionConcerns: Joi.array().items(Joi.string()),
        additionalVisionConcerns: Joi.string().allow(null, ""),
        eyeHealthConcerns: Joi.array().items(Joi.string()),
        additionalEyeHealthConcerns: Joi.string().allow(null, ""),
        previousOcularConditions: Joi.array().items(Joi.string()),
        ocularSurgeries: Joi.object({
            hadOcularSurgeryInThePast: Joi.boolean(),
            specifyTypeOfSurgery: Joi.string().allow(null, ""),
            additionalDiagnosedCondition: Joi.string().allow(null, "")
        }),
        additionalOcularConditions: Joi.string().allow(null, ""),
        socialHistory: Joi.object({
            occupation: Joi.string(),
            hobbies: Joi.string(),
            dailyHoursOnComputer: Joi.string()
        }),
        familyHistory: Joi.array().items(Joi.object({ ...idNameSchema })),
        otherFamilyHistory: Joi.string(),
        prescriptionHistoryAndAllergies: Joi.array().items(
            Joi.object({
                category: Joi.string(),
                drugName: Joi.string(),
                duration: Joi.string(),
                frequency: Joi.string(),
                foodAllergies: Joi.object({ ...idNameSchema })
            })
        ),
        comment: Joi.string(),
        summaryText: Joi.string(),
        ...metadataSchema
    }),
    fundus: Joi.array().items(
        Joi.object({
            ...rightLeftEyeIdNameSchema,
            summaryText: Joi.string(),
            comment: Joi.string(),
            ...metadataSchema
        })
    ),
    extraOcularMuscleMovement: Joi.array().items(
        Joi.object({
            superiorRectus: rightLeftEyeSchema,
            inferiorRectus: rightLeftEyeSchema,
            lateralRectus: rightLeftEyeSchema,
            medialRectus: rightLeftEyeSchema,
            superiorOblique: rightLeftEyeSchema,
            inferiorOblique: rightLeftEyeSchema,
            rightEye: Joi.string(),
            leftEye: Joi.string(),
            comment: Joi.string(),
            summaryText: Joi.string(),
            ...metadataSchema
        })
    ),
    posteriorSegmentExam: Joi.object({
        disc: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        vessels: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        periphery: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        macular: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        retina: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        vitreous: Joi.array().items(
            Joi.object({
                rightEye: Joi.string(),
                leftEye: Joi.string()
            })
        ),
        comment: Joi.string(),
        summaryText: Joi.string(),
        ...metadataSchema
    }),
    followUp: Joi.object({ ...followUpRequestSchema }),
    linkedVisit: Joi.string(),
    findings: Joi.string(),
    recommendations: Joi.array()
        .items(Joi.object({ _id: Joi.string(), name: Joi.string() }))
        .unique()
});

export const admissionRequestSchema = Joi.object({
    reason: Joi.string().required(),
    diagnosis: Joi.string().required(),
    specialInstruction: Joi.string(),
    startDate: Joi.date().required(),
    endDate: Joi.date(),
    appointmentId: Joi.string().required()
});

export const procedureRequestSchema = Joi.object({
    appointmentId: Joi.string(),
    procedureType: Joi.string().valid("inOffice", "internalLab", "externalLab").required(),
    category: Joi.string(),
    test: Joi.array().items(
        Joi.object({
            _id: Joi.string().required(),
            name: Joi.string().required()
        })
    ),
    branch: Joi.object({
        _id: Joi.string().required(),
        name: Joi.string().required()
    }),
    comment: Joi.string(),
    isUrgent: Joi.boolean(),
    procedureName: Joi.string().when("procedureType", {
        is: "externalLab",
        then: Joi.required(),
        otherwise: Joi.optional()
    }),
    hospital: Joi.string(),
    procedureId: Joi.string(),
    patient: Joi.object({
        _id: Joi.string(),
        mrn: Joi.string(),
        name: Joi.string()
    }).when("appointmentId", {
        is: Joi.string().exist(),
        then: Joi.forbidden(),
        otherwise: Joi.required()
    })
});

export const surgeryRequestSchema = Joi.object({
    bookSurgery: Joi.object({ ...bookSurgerySchema }).required(),
    ...specialtyRequest(),
    source: Joi.object({ ...idNameSchema }).required(),
    raiseBill: Joi.boolean(),
    checkin: Joi.boolean().default(false),
    theatre: Joi.string()
});

export const archiveRequestSchema = Joi.object({
    appointmentId: Joi.string().required(),
    reason: Joi.string().required()
});

export const getManySchema = Joi.object({
    patientId: Joi.string(),
    appointmentId: Joi.string(),
    isArchived: Joi.boolean(),
    specialty: Joi.string(),
    linkedVisit: Joi.string(),
    _id: Joi.string(),
    fromDate: Joi.date(),
    toDate: Joi.date().min(Joi.ref("fromDate"))
});

export const validateCreateRequest = async (req, res, next) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        const { visualAcuity, refraction, fundus } = req.body;

        const [appointment] = await Promise.all([
            AppointmentService.getOne(
                {
                    _id: new ObjectId(req.body.appointmentId),
                    appointmentState: appointmentStateConst.CONSULTING,
                    status: APPOINTMENT_STATUS.ACTIVE,
                    parentOrganizationId
                },
                { populate: populateConsultant, lean: false }
            ),
            ...[
                visualAcuity && validateVisualAcuity(visualAcuity),
                refraction && validateRefraction(refraction),
                fundus && validateFundus(fundus)
            ].filter(Boolean)
        ]);

        if (!appointment) return error(res, 400, "Case note cant be created at the moment, check appointment state.");

        res.locals = { appointment };
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const validateBookSurgery = async (req, res, next) => {
    const {
        bookSurgery: { typeOfSurgery },
        theatre,
        checkin,
        raiseBill,
        caseNoteId
    } = req.body;
    const { profile: operator, branchId, parentOrganizationId } = req.user.currentLocation;
    const { admission, appointment, patient } = res.locals;
    try {
        let branch;
        let items;
        let bill;
        if (!req.originalUrl.includes("inpatient")) {
            if (caseNoteId) {
                const casenote = await CaseNote.findOne({ _id: new ObjectId(caseNoteId), parentOrganizationId }).lean();
                if (!casenote) return error(res, 404, "Case note record not found");
                req.body.bookSurgery.caseNoteId = casenote._id;
            }
        }

        if (raiseBill) {
            const { branch: branc, items: it } = await composeSurgeryPayload(res, typeOfSurgery, branchId, operator);
            branch = branc;
            bill = {
                items: it,
                proforma: true,
                patient: patient ? patient._id : null,
                operator: operator,
                parentOrganizationId,
                branchId,
                totalDue: branch?.price,
                status: "pending",
                consultant: appointment?.consultant || admission?.doctor || operator
            };
        }

        if (theatre) {
            await confirmTheatreAvailability(theatre, parentOrganizationId);
        }

        if (checkin) {
            req.body = {
                theatre,
                "bookSurgery.status": bookSurgeryStatus["IN PROGRESS"],
                "bookSurgery.checkInDate": new Date(),
                ...req.body
            };
        }

        res.locals = {
            ...res.locals,
            items,
            branch,
            bill
        };
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const validateRaiseBillDoc = async (req, res, next) => {
    try {
        const { parentOrganizationId, profile: operator, _id: branchId } = req.user.currentLocation;
        const { item, test, services, appointmentId, procedureType, patient } = req.body;
        let branch;
        let items;
        let appointment = null;
        let admission = null;
        if (
            !req.originalUrl.includes("tasks") &&
            !req.originalUrl.includes("inpatient") &&
            !req.originalUrl.includes("surgery")
        ) {
            appointment = await Appointment.findOne({ _id: appointmentId, parentOrganizationId }).lean();
            if (!appointment) {
                appointment = {
                    patient,
                    consultant: operator
                };
            }
        }
        if (req.originalUrl.includes("inpatient") && !req.originalUrl.includes("surgery")) {
            admission = await Admission.findOne({ _id: req.params._id, parentOrganizationId });
            if (!admission) return error(res, 404, "Admission not found");
            if (admission.checkOutDate && isAfter(new Date(), new Date(admission.checkOutDate))) {
                return error(res, 400, "Admission checkout date has elapsed");
            }
        }

        if (req.originalUrl.includes("surgery")) {
            if (isEnv("production")) {
                const { branch: location, items: it } = await composeSurgeryPayload(res, item, branchId, operator);
                items = it;
                branch = location;
            } else {
                const {
                    bookSurgery: { amount, typeOfSurgery }
                } = req.body;
                if (amount) {
                    const { branch: location, items: it } = await composeSurgeryPayload(
                        res,
                        typeOfSurgery,
                        branchId,
                        operator
                    );
                    items = it;
                    branch = location;
                }
                appointment = res.locals.appointment;
            }
        }

        if (req.originalUrl.includes("procedure") && !procedureType.includes("externalLab")) {
            branch = {};
            branch.items = await procedurePayload(res, test, branchId, operator);
            let total = 0;
            items = branch.items;
            items.forEach((it) => {
                total += it.price;
                it.section = "clinic";
            });
            branch.price = total;
        }

        if (req.originalUrl.includes("tasks")) {
            branch = {};
            branch.items = await labPayload(res, services, branchId, operator);
            let total = 0;
            items = branch.items;
            items.forEach((it) => {
                total += it.price;
                it.section = "lab";
            });
            branch.price = total;
        }

        res.locals = {
            bill: {
                items: items,
                proforma: true,
                patient: appointment ? appointment.patient._id : null,
                operator: operator,
                parentOrganizationId,
                branchId,
                totalDue: branch?.price,
                status: "pending",
                consultant: appointment ? appointment.consultant : operator
            },
            admission,
            appointment
        };
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const validateUpdateRequest = async (req, res, next) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        let caseNote = await CaseNoteService.getOne(
            { _id: req.params._id, parentOrganizationId },
            { populate: "appointmentId" }
        );

        if (!caseNote) return error(res, 404, "Record not found");

        if (hasExceededGracePeriod(caseNote.appointmentId.appointmentDate)) {
            return error(res, 400, "Appointment Ended Can't Edit CaseNote");
        }

        caseNote = { ...caseNote, appointmentId: caseNote.appointmentId?._id };
        res.locals = { caseNote };
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};
