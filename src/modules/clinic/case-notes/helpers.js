/* eslint-disable no-nested-ternary */
import AuditTrail from "admin/audit-trail/model";
import { transferSection } from "clinic/patient-transfer/constant";
import PatientTransfer from "clinic/patient-transfer/model";
import { addDays, isAfter, startOfDay } from "date-fns";
import Patient from "frontdesk/patients/model";
import { useSpecialtyQuery } from "helpers/constants";
import { error } from "helpers/response";
import { isArray, isObject, removePropertiesFromObject } from "helpers/utils";
import Item from "inventory/items/model";
import { isEqual, isPlainObject } from "lodash";
import diff from "microdiff";
import { sendNotification } from "modules/app/notifications/controller";
import { model, Types } from "mongoose";
import { uploadFiles } from "services/storage";
import throwErrorWithCode from "utils/throw-error";
import { DROPDOWN_MODEL } from "modules/dropdowns/dropdown-core/constants";
import { patientsRecordToExtract, SECTIONS_TO_ANNOTATE } from "./constant";

const { ObjectId } = Types;

/**
 * Checks if a section has content that should be annotated
 *
 * @param {*} section - The section to check
 * @returns {boolean} True if the section has content, false otherwise
 */
const isFilled = (section) => {
    if ([null, undefined].includes(section)) return false;
    if (typeof section === "string") return section.trim() !== "";
    if (typeof section === "object") return Object.values(section).length > 0;
    if (isArray(section)) return section.length > 0 && section.some((item) => isFilled(item));
    return true;
};

/**
 * Gets the metadata to add to a section
 *
 * @param {*} req - The request object
 * @returns {Object} The metadata object
 */
const getMetadata = (req, { updateOnly = false } = {}) => {
    const { _id, name } = req.user.currentLocation.profile;
    const createdBy = { _id, name };
    const updateMeta = { updatedBy: createdBy, updatedAt: new Date() };
    return updateOnly ? updateMeta : { ...updateMeta, createdBy, createdAt: new Date() };
};

/**
 * Adds metadata to an object section
 *
 * @param {Object} section - The section object to annotate
 * @param {Object} metadata - The metadata to add to the section
 * @returns {Object} The section with added metadata
 */
const annotateObject = (section, metadata) => {
    return { ...section, ...metadata };
};
/**
 * Annotates objects in an array with metadata
 *
 * @param {*} section - The array of objects to annotate
 * @param {*} metadata - The metadata to add to each object
 * @returns {Array} The annotated array
 */
const annotateObjectsInArray = (section, metadata) => {
    return section.map((item) => {
        if (isObject(item)) return annotateObject(item, metadata);
        return item;
    });
};

const annotateSection = (section, metadata) => {
    if (isObject(section)) return annotateObject(section, metadata);
    if (isArray(section)) return annotateObjectsInArray(section, metadata);
    return section;
};

/**
 * Annotates specific sections of a case note document with metadata
 *
 * @param {Object} req - The request object containing user information and case note data
 * @returns {Object} The annotated case note document
 *
 * @description
 * This function adds metadata (creator, updater, timestamps) to specific sections
 * of a case note as defined in SECTIONS_TO_ANNOTATE. It handles both object and array
 * data structures, and only annotates sections that have content.
 *
 * The metadata includes:
 * - createdBy: The user who created the section
 * - updatedBy: Initially the same as createdBy
 * - createdAt: Timestamp of creation
 * - updatedAt: Initially the same as createdAt
 *
 * @example
 * // Before: { complaint: [{ summary: "Patient reports headache" }] }
 * // After:
 * // { complaint: [{ summary: "Patient reports headache", createdBy: {...},
 * // updatedBy: {...}, createdAt: Date, updatedAt: Date }] }
 */
export const annotateNewCaseNoteDoc = (req) => {
    const annotatedDoc = { ...req.body };
    const metadata = getMetadata(req);
    return Object.keys(annotatedDoc).reduce((acc, key) => {
        const section = annotatedDoc[key];
        if (SECTIONS_TO_ANNOTATE.includes(key) && isFilled(section)) acc[key] = annotateSection(section, metadata);
        else acc[key] = section;
        return acc;
    }, annotatedDoc);
};

export const composeDoc = async (req, res) => {
    const { profile, _id: branchId, parentOrganizationId } = req.user.currentLocation;
    const { appointment } = res.locals;

    if (req.body.attachments) {
        req.body.attachments = await uploadFiles(req.body.attachments, "case-notes-attachements");
    }
    req.body = annotateNewCaseNoteDoc(req);

    return {
        ...req.body,
        createdBy: profile,
        updatedBy: profile,
        branchId,
        parentOrganizationId,
        patient: appointment.patient,
        consultant: appointment.consultant
    };
};

export const composeSurgeryPayload = async (res, item, branchId, operator) => {
    const service = await Item.findOne({ _id: item._id }).lean();
    if (!service) return error(res, 400, "Selected surgery not found");
    const data = {};
    data.branch = service.branches.find(({ _id }) => String(_id) === String(branchId));
    const itemPayload = {
        _id: item._id,
        name: item.name,
        price: data.branch.price,
        quantity: 1,
        operator: operator,
        source: { name: "Surgery", _id: item._id },
        section: "clinic",
        branch: data.branch._id
    };
    data.items = [itemPayload];
    return data;
};

export const procedurePayload = async (res, test, branchId, operator) => {
    return Promise.all(
        test.map(async (item) => {
            const service = await Item.findOne({ _id: item._id }).lean();
            if (!service) return error(res, 400, "There's a problem with some selected tests");
            const branch = service.branches.find(({ _id }) => String(_id) === String(branchId));
            item.price = branch && branch.price ? branch?.price : 0;
            item.quantity = 1;
            item.operator = operator;
            item.source = { name: "Procedure", _id: item._id };
            return item;
        })
    );
};

export const labPayload = async (res, services, branchId, operator) => {
    return Promise.all(
        services.map(async (service) => {
            const item = await Item.findOne({ _id: service }).lean();
            if (!item) return error(res, 400, "There's am issue with some of the selected services");
            const branch = item.branches.find(({ _id }) => String(_id) === String(branchId));
            const it = {};
            it._id = service;
            it.name = item.name;
            it.price = branch.price;
            it.quantity = 1;
            it.operator = operator;
            it.source = { name: "Lab", _id: service };
            return it;
        })
    );
};

const caseNoteTrail = (operation, prev, current, key) => {
    if (operation === "insert") {
        if (key === "complaint") {
            return `${current} was added to summary`;
        }
        if (key === "surgery") {
            return `${current} was added as doctor`;
        }
        if (typeof current === "undefined") return "No information added";
        return `${current} was added`;
    }
    return `Changed ${prev} to ${current}`;
};

export const extractCaseNoteRecord = async (
    req,
    previousReq,
    operation,
    { operator, parentOrganizationId, branchId, documentId },
    snapshot
) => {
    const caseNoteRecord = [
        {
            ...(previousReq?.complaint?.length &&
                req?.complaint?.length && {
                    complaint: caseNoteTrail(
                        operation,
                        previousReq.complaint[0]?.summary,
                        req.complaint[0]?.summary,
                        "complaint"
                    )
                }),
            systemReview: caseNoteTrail(
                operation,
                previousReq?.systemReview?.describeComplaint,
                req?.systemReview?.describeComplaint,
                "systemReview"
            ),
            medicalHistory: caseNoteTrail(
                operation,
                previousReq?.medicalHistory?.personalHistory?.summary,
                req?.medicalHistory?.personalHistory?.summary,
                "medicalHistory"
            ),
            physicalExamination: caseNoteTrail(
                operation,
                previousReq?.physicalExamination?.summary,
                req?.physicalExamination?.summary,
                "physicalExamination"
            ),
            narrative: caseNoteTrail(operation, previousReq?.narrative, req?.narrative, "narrative"),
            procedure: caseNoteTrail(
                operation,
                previousReq?.procedure?.procedureType,
                req?.procedure?.procedureType,
                "procedure"
            ),
            surgery: caseNoteTrail(operation, previousReq?.surgery?.doctor?.name, req?.surgery?.doctor?.name, "surgery")
        }
    ];
    await AuditTrail.create({
        operator,
        branchId,
        snapshot,
        ...(operation === "update" && {
            changes: { ...previousReq }
        }),
        documentId,
        caseNoteRecord,
        action: operation,
        coll: "case-note",
        parentOrganizationId,
        operationType: operation
    });
    return caseNoteRecord;
};

const socketDetails = {
    internalLab_sponsor_true: { rooms: ["laboratory"], events: "newInternalLabProcedure" },
    internalLab_sponsor_false: { rooms: ["laboratory", "frontdesk"], events: "newSponsoredLabInvestigation" },
    inOffice_sponsor_true: { rooms: ["laboratory"], events: "newInOfficeProcedure" },
    inOffice_sponsor_false: { rooms: ["laboratory", "frontdesk"], events: "newInOfficeSponsorProcedure" }
};

export const composeProcedureAlertData = (appointment, admission) => {
    const name = appointment?.appointmentStateHistory[1]?.staffName || admission?.doctor?.name;
    const patientName = appointment?.patient?.name || admission?.patient?.name;
    const purpose = appointment?.appointmentPurpose?.name || "Procedure";
    const data = `${name} has transferred ${patientName} for ${purpose}`;
    return data;
};

export const internalLabProcedureAlert = async (req, data, doc) => {
    const { parentOrganizationId } = req.user.currentLocation;
    const { procedureType, sponsor } = req.body;

    const { rooms, event } = socketDetails[`${procedureType}_sponsor_${Boolean(sponsor)}`];
    const payload = {
        rooms,
        event,
        data,
        doc,
        parentOrganizationId
    };

    await sendNotification(payload);
};

export const composeFilter = (dto) => {
    const {
        parentOrganizationId,
        patientId,
        appointmentId,
        isArchived,
        fromDate,
        toDate,
        specialty,
        linkedVisit,
        _id
    } = dto;
    let filter = { isArchived: false, parentOrganizationId };
    if (patientId) {
        filter = { ...filter, "patient._id": patientId };
    }

    if (appointmentId) {
        filter = { ...filter, appointmentId };
    }

    if (isArchived) {
        filter = { ...filter, isArchived };
    }

    if (fromDate && toDate) {
        filter = { ...filter, createdAt: { $gte: fromDate, $lte: toDate } };
    }

    if (specialty) {
        filter = { ...filter, ...useSpecialtyQuery(specialty) };
    }

    if (linkedVisit) {
        filter = { ...filter, linkedVisit };
    }

    if (_id) {
        filter = { ...filter, _id: new ObjectId(_id) };
    }
    if (dto.complaint) filter.complaint = dto.complaint;

    return filter;
};

const removeSensitiveProperties = (record, sensitiveProperties) => {
    return removePropertiesFromObject(record, sensitiveProperties);
};

const composeLog = (logs) => {
    return logs.map((log) => ({ changes: { ...log.snapshot?.toJSON(), ...log.changes?.toJSON() } }));
};

export const validateUpdateDifference = async (req, casenote) => {
    const { profile: operator } = req.user.currentLocation;

    const logs = await AuditTrail.find({ documentId: casenote._id });
    const changes = {};
    const sensitiveProperties = ["linkedVisit", "isArchived", "reasonToArchive"];
    const strippedCasenote = removeSensitiveProperties(casenote, sensitiveProperties);
    Object.keys(strippedCasenote).forEach((key) => {
        if (!isEqual(strippedCasenote[key], req.body[key])) {
            changes[key] = req.body[key];
        }
    });

    if (Object.keys(changes).length) {
        const newLogs = composeLog(logs);
        const key = Object.keys(changes)[0];
        const val = newLogs.find(
            (log) =>
                // eslint-disable-next-line no-prototype-builtins
                log.changes.hasOwnProperty(key) && !isEqual(log.changes[key] !== changes[key])
        );

        if (val.changes.operator._id !== operator._id) {
            throwErrorWithCode("You are not allowed to change information added by someone else.", 403);
        }
    }

    return changes;
};

export const checkForTransferedPatient = async (res, operator, transferId) => {
    try {
        let patientTransfer;
        if (transferId) {
            const query = {
                _id: new ObjectId(transferId),
                $or: [
                    { section: transferSection.OUTPATIENTPROCEDURE },
                    { section: transferSection.OUTPATIENTSURGERY },
                    { section: transferSection.CONTINUEDCONSULTATION }
                ]
            };
            patientTransfer = await PatientTransfer.findOne(query);
            const currentTransferState = patientTransfer.actions.find(
                (action) => action.status === patientTransfer.status
            );
            if (operator._id !== currentTransferState.operator._id) {
                return error(res, 400, "You cannot edit casenote");
            }
        }

        return patientTransfer;
    } catch (err) {
        return error(res, 500, err);
    }
};

export const composeLabDoc = async (req) => {
    const patient = await Patient.findOne({ _id: new ObjectId(req.body.patient._id) }).lean();
    const patientPayload = {
        _id: patient._id,
        name: patient.name,
        email: patient.email,
        phoneNumber: patient.phoneNumber,
        address: patient.address
    };
    const services = [];
    req.body.test.forEach((test) => {
        services.push(test._id);
    });
    const payload = {};
    payload.patient = patientPayload;
    payload.dueDate = req.body.dateRequested;
    payload.services = services;

    return payload;
};

export const composeOrderDoc = async (req) => {
    const { profile: operator, parentOrganizationId, _id: branchId } = req.user.currentLocation;
    const payload = await composeLabDoc(req);
    const doc = { ...payload, operator, parentOrganizationId };
    if (req.body.procedureType === "internalLab") {
        doc.branchId = req.body.branch._id;
    } else {
        doc.branchId = branchId;
    }
    doc.status = "pending";
    if (req.body.isUrgent) {
        doc.urgent = req.body.isUrgent;
    }
    return doc;
};

export const composeProcedureDoc = (req, admission, appointment) => {
    const { parentOrganizationId, _id: branchId, profile: operator } = req.user.currentLocation;
    req.body.dateRequested = new Date();
    req.body.requestedBy = operator;
    req.body.admission = admission?._id;
    req.body.patient = req.originalUrl.includes("inpatient") ? admission.patient : appointment.patient;
    req.body.section = req.originalUrl.includes("inpatient") ? "inpatient" : "outpatient";
    return {
        branchId,
        parentOrganizationId,
        specialty: admission ? admission.specialty : appointment?.specialty,
        ...req.body
    };
};

export const hasExceededGracePeriod = (dateOfEntry) => {
    const date = startOfDay(addDays(new Date(dateOfEntry), 1));
    const gracePeriod = addDays(new Date(date), 3);
    return isAfter(new Date(), gracePeriod);
};

export const extractPatientData = (casenote, fields, type, sourceValue) => {
    const allFields = fields.split(".");
    const value = allFields.reduce((acc, key) => (acc ? acc[key] : undefined), casenote);

    const result = [];
    if (Array.isArray(value)) {
        result.push(
            ...value.map((v) => {
                return {
                    ...(typeof v === "string" && {
                        name: v,
                        type
                    }),
                    ...(isPlainObject(v) && {
                        name: v[sourceValue],
                        type
                    })
                };
            })
        );
    }

    if (isPlainObject(value)) {
        result.push({
            source: { _id: casenote._id, name: "CaseNote" },
            sourceField: fields,
            name: value[sourceValue],
            type
        });
    }

    return result;
};

export const getPatientHistory = (caseNote) => {
    const result = patientsRecordToExtract.flatMap((record) => {
        return extractPatientData(caseNote, record.fields, record.type, record.sourceValue).filter((d) => d.name);
    });
    return result;
};

const getNestedValue = (obj, path) => path.reduce((acc, key) => (acc && acc[key] ? acc[key] : []), obj);

const concatEyes = (data, parentField, nestedPath = []) => {
    const rightEye =
        data?.flatMap((item) => {
            const nestedArray = getNestedValue(item[parentField], nestedPath);
            return nestedArray.map((eye) => eye.rightEye?._id).filter(Boolean);
        }) || [];

    const leftEye =
        data?.flatMap((item) => {
            const nestedArray = getNestedValue(item[parentField], nestedPath);
            return nestedArray.map((eye) => eye.leftEye?._id).filter(Boolean);
        }) || [];

    return rightEye.concat(leftEye);
};

export const validateVisualAcuity = async (visualAcuity) => {
    const shIds = concatEyes(visualAcuity, "sh", []);
    const phIds = concatEyes(visualAcuity, "ph", []);
    const ccIds = concatEyes(visualAcuity, "cc", []);

    const nearIds = concatEyes(visualAcuity, "near", []);

    const accutyIds = [...shIds, ...phIds, ...ccIds];

    const [acuities, nears] = await Promise.all([
        accutyIds.length && model("VisualAcuity").find({ _id: { $in: accutyIds } }),
        nearIds.length && model("NearVisualAcuity").find({ _id: { $in: [...nearIds] } })
    ]);

    if (nearIds.length && nears.length !== nearIds.length) {
        throwErrorWithCode("One/more Near VA not found", 404);
    }

    if (accutyIds.length) {
        accutyIds.forEach((_id) => {
            const acuityExist = acuities.find((acuity) => String(acuity._id) === String(_id));
            if (!acuityExist) {
                const label = shIds.includes(_id) ? "sh" : phIds.includes(_id) ? "ph" : "cc";
                throwErrorWithCode(`One/more ${label} not found`, 404);
            }
        });
    }

    return { acuities, nears };
};

export const validateRefraction = async (refraction) => {
    const dvSphIds = concatEyes(refraction, "subjective", ["dv", "sph"]);
    const dvCylds = concatEyes(refraction, "subjective", ["dv", "cyl"]);
    const dvAxIds = concatEyes(refraction, "subjective", ["dv", "ax"]);
    const dvVaIds = concatEyes(refraction, "subjective", ["dv", "va"]);

    const nvSphIds = concatEyes(refraction, "subjective", ["nv", "sph"]);
    const nvCylds = concatEyes(refraction, "subjective", ["nv", "cyl"]);
    const nvAxIds = concatEyes(refraction, "subjective", ["nv", "ax"]);
    const nvVAIds = concatEyes(refraction, "subjective", ["nv", "va"]);

    const habitualRxSphIds = concatEyes(refraction, "habitualRx", ["spherical"]);
    const habitualRxCylds = concatEyes(refraction, "habitualRx", ["cyl"]);
    const habitualRxAxisIds = concatEyes(refraction, "habitualRx", ["axis"]);

    const finalRxSphIds = concatEyes(refraction, "finalRx", ["spherical"]);
    const finalRxCylds = concatEyes(refraction, "finalRx", ["cyl"]);
    const finalRxAxisIds = concatEyes(refraction, "finalRx", ["axis"]);
    const finalRxVaIds = concatEyes(refraction, "finalRx", ["va"]);

    const refractionCycloSphIds = concatEyes(refraction, "refractionCyclo", ["sph"]);
    const refractionCycloCylds = concatEyes(refraction, "refractionCyclo", ["cyl"]);
    const refractionCycloAxisIds = concatEyes(refraction, "refractionCyclo", ["ax"]);

    const postMydriaticTestSphIds = concatEyes(refraction, "postMydriaticTest", ["sph"]);
    const postMydriaticTestCylds = concatEyes(refraction, "postMydriaticTest", ["cyl"]);
    const postMydriaticTestAxisIds = concatEyes(refraction, "postMydriaticTest", ["ax"]);

    const retinoscopySphIds = concatEyes(refraction, "retinoscopy", ["sph"]);
    const retinoscopyCylds = concatEyes(refraction, "retinoscopy", ["cyl"]);
    const retinoscopyAxisIds = concatEyes(refraction, "retinoscopy", ["ax"]);

    const k1Ids = concatEyes(refraction, "keratometry", ["k1"]);
    const k2Ids = concatEyes(refraction, "keratometry", ["k2"]);
    const keratometryAxisIds = concatEyes(refraction, "keratometry", ["axis"]);
    const kAstigmatismIds = concatEyes(refraction, "keratometry", ["astigmatism"]);

    const cylIds = [
        ...dvCylds,
        ...nvCylds,
        ...habitualRxCylds,
        ...finalRxCylds,
        ...retinoscopyCylds,
        ...refractionCycloCylds,
        ...postMydriaticTestCylds
    ];

    const kIds = [...k1Ids, ...k2Ids];

    const axisIds = [
        ...dvAxIds,
        ...nvAxIds,
        ...habitualRxAxisIds,
        ...finalRxAxisIds,
        ...refractionCycloAxisIds,
        ...retinoscopyAxisIds,
        ...postMydriaticTestAxisIds,
        ...keratometryAxisIds
    ];

    const sphIds = [
        ...dvSphIds,
        ...nvSphIds,
        ...habitualRxSphIds,
        ...finalRxSphIds,
        ...refractionCycloSphIds,
        ...retinoscopySphIds,
        ...postMydriaticTestSphIds
    ];

    const vaIds = [...dvVaIds, ...nvVAIds, ...finalRxVaIds];

    const [cylinders, keros, astigmatisms, axes, spheres, acuities] = await Promise.all([
        cylIds.length && model("CylindricalDioptre").find({ _id: { $in: cylIds } }),
        kIds.length && model("KeratometryDiopter").find({ _id: { $in: kIds } }),
        kAstigmatismIds.length && model("AstigmatismDiopter").find({ _id: { $in: kAstigmatismIds } }),
        axisIds.length && model("Axis").find({ _id: { $in: axisIds } }),
        sphIds.length && model("SphericalDiopter").find({ _id: { $in: sphIds } }),
        vaIds.length && model("VisualAcuity").find({ _id: { $in: vaIds } })
    ]);

    if (kAstigmatismIds.length && astigmatisms.length !== kAstigmatismIds.length) {
        throwErrorWithCode("One/more Astigmatisms not found", 404);
    }

    if (cylIds.length && cylinders.length !== cylIds.length) {
        throwErrorWithCode("One/more cyl not found", 404);
    }

    if (kIds.length && keros.length !== kIds.length) {
        throwErrorWithCode("One/more k1 not found", 404);
    }

    if (axisIds.length && axes.length !== axisIds.length) {
        throwErrorWithCode("One/more axis not found", 404);
    }

    if (sphIds.length && spheres.length !== sphIds.length) {
        throwErrorWithCode("One/more spheres not found", 404);
    }

    if (vaIds.length && acuities.length !== vaIds.length) {
        throwErrorWithCode("One/more  Visual Acuity not found", 404);
    }

    return { cylinders, keros, astigmatisms, axes, spheres, acuities };
};

export const validateFundus = async (fundus) => {
    const rightEyeIds = fundus?.map(f => f.rightEye?._id).filter(Boolean) || [];
    const leftEyeIds = fundus?.map(f => f.leftEye?._id).filter(Boolean) || [];

    const allFundusIds = [...(new Set([...rightEyeIds, ...leftEyeIds]))];

    if (allFundusIds.length === 0) return;

    const fundusValues = await model(DROPDOWN_MODEL.FundusVessels).find({
        _id: { $in: allFundusIds }
    });

    if (fundusValues.length !== allFundusIds.length) {
        throwErrorWithCode("One/more Fundus values not found", 404);
    }
};

const metaFields = ["_id", "__v", "createdBy", "updatedBy", "createdAt", "updatedAt"];

/**
 * Checks if a field name is a metadata field
 *
 * @param {string} fieldName - The name of the field to check
 * @returns {boolean} True if the field is a metadata field, false otherwise
 */
const hasMetadataField = (fieldName) => metaFields.includes(fieldName);

/**
 * Determines if a path in a diff should be skipped because it points to metadata
 *
 * @param {Array<string|number>} path - The path array from a diff object
 * @returns {boolean} True if the path should be skipped, false otherwise
 */
const shouldSkipMetadataPath = (path) => {
    return hasMetadataField(path[path.length - 1]);
};

/**
 * Gets the differences between two objects, excluding metadata fields
 *
 * @param {Object} existingSection - The original section object
 * @param {Object} section - The new section object to compare against
 * @returns {Array} Array of differences, excluding metadata fields
 */
const getDifferences = (existingSection, section) => {
    return diff(existingSection, section).filter((difference) => !shouldSkipMetadataPath(difference.path));
};

/**
 * Finds a matching entry in the existing entries array based on content
 *
 * @param {Object} newEntry - The new entry to find a match for
 * @param {Array} existingEntries - Array of existing entries to search in
 * @returns {Object|null} The matching existing entry or null if not found
 */
const findMatchingExistingEntry = (newEntry, existingEntries) => {
    if (!isObject(newEntry) || !existingEntries?.length) return null;

    return existingEntries.find(
        (existing) =>
            isObject(existing) &&
            // Match by content (excluding metadata)
            Object.keys(newEntry).some(
                (itemKey) => !hasMetadataField(itemKey) && existing[itemKey] === newEntry[itemKey]
            )
    );
};
/**
 * Processes a single entry in an array, determining whether to update metadata
 *
 * @param {*} newEntry - The new entry to process
 * @param {Array} existingEntries - Array of existing entries to compare against
 * @param {Object} obj.newMeta - The metadata to add if the entry is new
 * @param {Object} obj.updateMeta - The metadata to add if the entry is updated
 * @returns {*} The processed entry with appropriate metadata
 */
const processArrayEntry = (newEntry, existingEntries, { newMeta, updateMeta }) => {
    if (!isObject(newEntry)) return newEntry;

    const existingEntry = findMatchingExistingEntry(newEntry, existingEntries);
    if (!existingEntry) return { ...newEntry, ...newMeta };

    // Check if this specific item has changes
    const changes = diff(existingEntry, newEntry).filter((d) => !hasMetadataField(d.path[0]));

    // If changed, update metadata
    if (changes.length > 0) return { ...newEntry, ...updateMeta };

    // If unchanged, preserve original metadata
    const { createdBy, createdAt, updatedBy, updatedAt } = existingEntry;
    return { ...newEntry, createdBy, createdAt, updatedBy, updatedAt };
};

/**
 * Processes an array section, handling metadata for each entry
 *
 * @param {Array} section - The array section to process
 * @param {Array} existingSection - The existing array section to compare against
 * @param {Object} obj.newMeta - The metadata to add to new entries
 * @param {Object} obj.updateMeta - The metadata to add to updated entries
 * @returns {Array} The processed array with appropriate metadata
 */
const handleArraySection = (section, existingSection, { newMeta, updateMeta }) => {
    const existingEntries = existingSection || [];
    return section.map((newEntry) => processArrayEntry(newEntry, existingEntries, { newMeta, updateMeta }));
};

/**
 * Annotates updates to a case note document with metadata
 *
 * @param {Object} req - The request object containing user information
 * @param {Object} existingCasenote - The existing case note document
 * @returns {Object} The updated case note document with annotations
 *
 * @description
 * This function compares the request body with the existing case note and
 * adds appropriate metadata (updater, timestamp) to sections that have changed.
 * It preserves original creation metadata for unchanged sections and adds
 * new metadata for new or modified sections.
 *
 * The function handles both object and array data structures, and only
 * annotates sections that are defined in SECTIONS_TO_ANNOTATE.
 */
export const annotateUpdatesToCasenote = (req, res) => {
    const { caseNote: existingCasenote } = res.locals;
    const newMeta = getMetadata(req);
    const updateMeta = getMetadata(req, { updateOnly: true });

    return Object.keys(req.body).reduce((acc, key) => {
        const section = req.body[key];
        const existingSection = existingCasenote[key];

        if (!SECTIONS_TO_ANNOTATE.includes(key) || !isFilled(section)) {
            acc[key] = section;
            return acc;
        }

        // Handle case where the section wasn't filled before
        if (!existingSection) {
            acc[key] = annotateSection(section, newMeta);
            return acc;
        }

        const differences = getDifferences(existingSection, section);
        if (!differences.length) {
            acc[key] = section;
            return acc;
        }

        if (isObject(section)) acc[key] = annotateObject(section, updateMeta);
        else if (isArray(section)) acc[key] = handleArraySection(section, existingSection, { newMeta, updateMeta });
        else acc[key] = section;

        return acc;
    }, {});
};
