import { error } from "helpers/response";
import Transfer from "clinic/patient-transfer/model";
import Jo<PERSON> from "joi";
import { Types } from "mongoose";
import { idNameRequestSchema, specialtyRequest } from "helpers/constants";
import Patient from "frontdesk/patients/model";
import { confirmAppointmentAvailability } from "frontdesk/appointments/helper";
import { transferStatus, transferSection } from "clinic/patient-transfer/constant";
import { validateStaffPin } from "clinic/patient-transfer/helper";

const { ObjectId } = Types;

export const createSchema = Joi.object({
    patient: Joi.string().required(),
    reason: Joi.string(),
    comment: Joi.string(),
    consultant: idNameRequestSchema(),
    to: Joi.string()
        .valid(...Object.values(transferSection))
        .required(),
    from: Joi.string()
        .valid(...Object.values(transferSection))
        .required(),
    visit: Joi.object({ _id: Joi.string().required(), name: Joi.string().required() }).required(),
    ...specialtyRequest()
});

export const updateStatusSchema = Joi.object({
    status: Joi.string()
        .valid(...Object.values(transferStatus))
        .required(),
    pin: Joi.string()
});

export const fetchSchema = Joi.object({
    status: Joi.string().valid(...Object.values(transferStatus)),
    to: Joi.string().valid(...Object.values(transferSection)),
    limit: Joi.number(),
    page: Joi.number(),
    visit: Joi.string(),
    specialty: Joi.string(),
    operator: Joi.string()
});

export const validateCreateRequest = async (req, res, next) => {
    const { patient, visit } = req.body;
    try {
        const [transfer, patientRecord] = await Promise.all([
            Transfer.findOne({ patient, status: "pending" }),
            Patient.findOne({ _id: new ObjectId(patient) })
        ]);
        if (transfer) return error(res, 400, `Patient is still in ${transfer.to}`);
        await confirmAppointmentAvailability(visit._id);

        res.locals = { patientRecord };
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const validateUpdateStatusRequest = async (req, res, next) => {
    const { status } = req.body;
    try {
        const transfer = await Transfer.findOne({ _id: req.params._id });
        if (!transfer) return error(res, 400, "Transfer not initiated for patient");
        if (transfer.status === status) return error(res, 400, `Patient already ${transfer.status}`);
        const accepted = transfer.actions.find((action) => action.status === transferStatus.ACCEPTED);
        if (!accepted && status !== transferStatus.ACCEPTED) {
            return error(res, 400, `Proceed to accept transfer`);
        }

        const consultationSections = [transferSection.OUTPATIENTCONSULTATION, transferSection.CONTINUEDCONSULTATION];
        if (status === transferStatus.CHECK_OUT && consultationSections.includes(transfer.to)) {
            await validateStaffPin(req, res);
        }

        res.locals = { transfer };
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};
