import { Types, startSession } from "mongoose";
import { error, success } from "helpers/response";
import Appointment from "frontdesk/appointments/model";
import { sendNotification } from "app/notifications/controller";
import * as PatientTransferService from "clinic/patient-transfer/service";
import { transferStatus } from "clinic/patient-transfer/constant";
import Comment from "app/comments/model";
import {
    composeDoc,
    composeCommentDoc,
    composeTransferNotification,
    composeTransferStatus,
    composePatientTransferPipeline,
    composeDailyTransferPipeline
} from "clinic/patient-transfer/helper";

const { ObjectId } = Types;

export const create = async (req, res) => {
    const { visit, comment } = req.body;
    try {
        let transfer;
        const doc = composeDoc(req);
        const session = await startSession();
        await session.withTransaction(async () => {
            transfer = await PatientTransferService.create(doc, session);
            await Appointment.updateOne(
                { _id: new ObjectId(visit._id) },
                { patientTransfer: transfer._id },
                { session }
            );
            if (comment) {
                const commentDoc = composeCommentDoc(req, transfer._id);
                await Comment.create([commentDoc], { session });
            }
        });
        await session.endSession();

        if (transfer) {
            const notificationPayload = composeTransferNotification(req, res, transfer);
            await sendNotification(notificationPayload);
        }
        return success(res, 200, transfer);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const updateStatus = async (req, res) => {
    try {
        let transfer;
        const doc = composeTransferStatus(req, res);
        const session = await startSession();
        await session.withTransaction(async () => {
            transfer = await PatientTransferService.update(req.params._id, doc, { session });
            if (doc.status === transferStatus.CHECK_OUT) {
                await Appointment.updateOne(
                    { _id: new ObjectId(transfer.visit._id) },
                    { patientTransfer: null },
                    { session }
                );
            }
        });
        await session.endSession();
        return success(res, 200, transfer);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetch = async (req, res) => {
    try {
        const pipeline = composePatientTransferPipeline();
        const result = await PatientTransferService.getMany(
            {
                ...req.query,
                pipeline
            },
            { dontPaginate: false }
        );
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const autoCheckout = async (_req, res) => {
    try {
        const query = {
            status: transferStatus.CHECK_IN
        };
        const transfers = await PatientTransferService.getMany(query, { dontPaginate: true });
        const transferIds = transfers.map((transfer) => transfer._id);

        if (transfers.length) {
            const session = await startSession();
            await session.withTransaction(async () => {
                await PatientTransferService.updateMany(
                    { _id: { $in: transferIds } },
                    { status: transferStatus.SYSTEM_DONE, checkOutDate: new Date() }
                );
                await Appointment.updateMany(
                    {
                        patientTransfer: { $in: transferIds }
                    },
                    { patientTransfer: null }
                );
            });
            await session.endSession();
        }

        return success(res, 200, {
            numberOfTransfers: transfers.length
        });
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetchDailyTransferRequest = async (req, res) => {
    try {
        const pipeline = composeDailyTransferPipeline();
        const result = await PatientTransferService.getMany(
            {
                ...req.query,
                pipeline
            },
            { dontPaginate: false }
        );
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};
