import { transferStatus } from "clinic/patient-transfer/constant";
import { endOfDay, format, startOfDay } from "date-fns";
import { error } from "helpers/response";
import { comparePassword } from "helpers/utils";
import { Types } from "mongoose";
import Staff from "settings/staff/model";

const { ObjectId } = Types;

export const composeDoc = (req) => {
    const { _id: branch, parentOrganizationId, profile: operator } = req.user.currentLocation;

    return {
        ...req.body,
        branch,
        actions: [
            {
                status: transferStatus.PENDING,
                operator,
                date: new Date()
            }
        ],
        parentOrganizationId
    };
};

export const composeCommentDoc = (req, transfer) => {
    const { profile: operator, parentOrganizationId, _id } = req.user.currentLocation;

    return {
        message: req.body.comment,
        source: {
            _id: transfer,
            name: "PatientTransfer"
        },
        operator,
        branch: _id,
        parentOrganizationId
    };
};

export const composeTransferStatus = (req, res) => {
    const { status } = req.body;
    const { transfer } = res.locals;
    const { profile: operator } = req.user.currentLocation;

    return {
        ...req.body,
        actions: [
            ...transfer.actions,
            {
                status,
                operator,
                date: new Date()
            }
        ]
    };
};

export const composeFilter = (dto) => {
    const { status, to, patient, visit, parentOrganizationId, branch, specialty, operator } = dto;
    let query = {
        parentOrganizationId,
        branch
    };

    if (status) query = { ...query, status };
    if (to) query = { ...query, to };
    if (patient) query = { ...query, patient };
    if (visit) query = { ...query, "visit._id": visit };
    if (specialty) query = { ...query, specialty };
    if (operator) query = { "operator._id": operator };

    return query;
};

export const validateStaffPin = async (req, res) => {
    const { profile: operator } = req.user.currentLocation;

    const staff = await Staff.findOne({ _id: new ObjectId(operator._id), isActive: true });
    if (!staff) return error(res, 404, "Staff not found");
    if (!staff.pin) return error(res, 400, "PIN not added to profile");
    const matched = comparePassword(staff.pin, req.body.pin);
    if (!matched) return error(res, 403, "The supplied PIN is incorrect");

    return staff;
};

export const composeTransferNotification = (req, res, transfer) => {
    const { profile: operator } = req.user.currentLocation;
    const { consultant } = req.body;
    const { patientRecord } = res.locals;

    const msg = `${operator.name} has transfered ${patientRecord.name} with MRN ${
        patientRecord.mrn
    } to your department. ${format(new Date(transfer.createdAt), "dd MM yyyy: hh:mm aaa")}`;

    const newNotification = {
        title: `${transfer.to} Transfer`,
        body: msg,
        topic: consultant?._id || operator._id,
        parentOrganizationId: transfer.parentOrganizationId,
        section: transfer.to
    };
    const payload = {
        rooms: consultant?._id || transfer.to,
        event: "newPatientTransfer",
        data: msg,
        doc: newNotification,
        parentOrganizationId: transfer.parentOrganizationId
    };

    return payload;
};

export const populants = [
    {
        path: "patient",
        select: "_id name email phoneNumber gender mrn"
    },
    {
        path: "visit._id",
        select: "appointmentDate appointmentStartDate appointmentPurpose consultant"
    }
];

export const composePatientTransferPipeline = () => {
    return [
        {
            $match: {
                createdAt: {
                    $gte: startOfDay(new Date()),
                    $lte: endOfDay(new Date())
                }
            }
        },
        {
            $lookup: {
                let: { visitId: { $toObjectId: "$visit._id" } },
                from: "appointments",
                pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$visitId"] } } }],
                as: "visit"
            }
        },
        {
            $lookup: {
                let: { patientId: { $toObjectId: "$patient" } },
                from: "patients",
                pipeline: [
                    { $match: { $expr: { $eq: ["$_id", "$$patientId"] } } },
                    {
                        $project: {
                            _id: 1,
                            name: 1,
                            email: 1,
                            phoneNumber: 1,
                            gender: 1,
                            mrn: 1
                        }
                    }
                ],
                as: "patient"
            }
        }
    ];
};

export const composeDailyTransferPipeline = () => {
    return [
        {
            $match: {
                createdAt: {
                    $gte: startOfDay(new Date()),
                    $lte: endOfDay(new Date())
                }
            }
        },
        {
            $lookup: {
                let: {
                    patientId: {
                        $toObjectId: "$patient"
                    }
                },
                from: "patients",
                as: "patients",
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $eq: ["$$patientId", "$_id"]
                            }
                        }
                    },
                    {
                        $project: {
                            name: 1,
                            mrn: 1
                        }
                    }
                ]
            }
        },
        {
            $lookup: {
                let: {
                    visitId: {
                        $toObjectId: "$visit._id"
                    }
                },
                from: "appointments",
                as: "visit",
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $eq: ["$$visitId", "$_id"]
                            }
                        }
                    },
                    {
                        $project: {
                            appointmentPurpose: 1,
                            appointmentDate: 1,
                            appointmentStartDate: 1
                        }
                    }
                ]
            }
        },
        {
            $group: {
                _id: "$visit._id",
                record: {
                    $push: "$$ROOT"
                }
            }
        }
    ];
};
