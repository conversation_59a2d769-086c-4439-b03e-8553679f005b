import { logChanges } from "admin/audit-trail/controller";
import { transferSection, transferStatus } from "clinic/patient-transfer/constant";
import { idNameRequiredSchema, idNameSchema, specialtySchema } from "helpers/constants";
import { model, Schema } from "mongoose";
import aggregatePaginator from "mongoose-aggregate-paginate-v2";
import paginator from "mongoose-paginate-v2";
import { composeIDSchema } from "utils/generate-id";

const visitSchema = new Schema({
    _id: { type: "String", required: true, index: true, refPath: "type" },
    name: { type: "String", required: true, default: "Appointment", enum: ["Appointment"] }
});

const actionSchema = new Schema({
    operator: idNameRequiredSchema,
    status: { type: "String", enum: Object.values(transferStatus) },
    date: { type: "Date" },
    _id: 0
});

const modelName = "PatientTransfer";

const schema = new Schema(
    {
        _id: composeIDSchema(modelName),
        to: { type: "String", enum: Object.values(transferSection) },
        from: { type: "String", enum: Object.values(transferSection) },
        status: { type: "String", enum: Object.values(transferStatus), default: transferStatus.PENDING },
        branch: { type: "String", required: true, ref: "Organization" },
        patient: { type: "String", required: true, ref: "Patient" },
        operator: idNameRequiredSchema,
        actions: [actionSchema],
        consultant: idNameSchema,
        parentOrganizationId: { type: "String", required: true },
        reason: { type: "String" },
        visit: visitSchema,
        ...specialtySchema()
    },
    { timestamps: true }
);

schema.index({ parentOrganizationId: 1, branch: 1, "visit._id": 1 });
schema.index({ to: 1, from: 1 });
schema.plugin(paginator);
schema.plugin(aggregatePaginator);
const Model = model(modelName, schema);

Model.watch({ fullDocument: "updateLookup" }).on("change", async (changeEvent) => {
    logChanges(changeEvent);
});
export default Model;
