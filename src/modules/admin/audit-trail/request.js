import Joi from "joi";

export const fetchSchema = Joi.object({
    actions: Joi.array().items(Joi.string()).unique(),
    field: Joi.string(),
    value: Joi.any().when("field", { is: Joi.exist(), then: Joi.required() }),
    operator: Joi.string(),
    branchId: Joi.string(),
    startDate: Joi.date(),
    endDate: Joi.date().when("startDate", {
        is: Joi.exist(),
        then: Joi.date().min(Joi.ref("startDate"))
    }),
    page: Joi.number(),
    limit: Joi.number()
});
