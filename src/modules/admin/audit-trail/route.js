import { isAuthorized } from "base/request";
import { Router } from "express";
import { validator } from "helpers/config";
import { fetch, fetchDocuments } from "admin/audit-trail/controller";
import { fetchSchema } from "admin/audit-trail/request";

const router = Router();

router.get("/audit-trail", isAuthorized(["view-audit-trail", "super-view"]), validator.query(fetchSchema), fetch);

router.get("/audit-trail/:_id", isAuthorized(["view-audit-trail", "super-view"]), fetchDocuments);

export default router;
