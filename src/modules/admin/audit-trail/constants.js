import { stockItemEvents } from "helpers/constants";

export const fieldsToExclude = {
    users: ["password", "token"],
    wallettransactions: "pin"
};

export const actionsToExclude = ["aggregated-stock-balances"];

export const operationTypes = {
    insert: "added",
    update: "updated",
    deactivate: "deactivated",
    delete: "deleted",
    reactivate: "reactivated"
};

export const deletableModels = [
    "Role",
    "Staff",
    "Shift",
    "Department",
    "SponsorPriceList",
    "CategoryDepartment",
    "MedicalRecordUpload"
];

export const deactivatableModels = [
    "Ward",
    "Room",
    "Bed",
    "AppointmentPurpose",
    "Supplier",
    "Item",
    "Category",
    "Department",
    "Organization",
    "Plan",
    "Role",
    "Staff",
    "Patient",
    "InpatientService",
    "Client",
    "Discount"
];

export const customLabels = [
    {
        label: "booked patient for admission",
        "data-collection": "admissions",
        "data-operationType": "insert"
    },
    {
        label: "scheduled appointment",
        value: "added-appointment",
        "data-collection": "appointments",
        "data-operationType": "insert"
    },
    {
        label: "linked category to departments",
        value: "added-category-department",
        "data-collection": "category-departments",
        "data-operationType": "insert"
    },
    {
        label: "raised bill",
        value: "added-bill",
        "data-collection": "bills",
        "data-operationType": "insert"
    },
    {
        label: "generated claims",
        value: "added-claims",
        "data-collection": "claims",
        "data-operationType": "insert"
    },
    {
        label: "added product/service",
        value: "added-items",
        "data-collection": "items",
        "data-operationType": "insert"
    },
    {
        label: "updated product/service",
        value: "updated-items",
        "data-collection": "items",
        "data-operationType": "insert"
    },
    {
        label: "added stock",
        value: "added-stock",
        "data-collection": "stocks",
        "data-operationType": "insert"
    },
    {
        label: "uploaded medical record",
        value: "uploaded-medical-record",
        "data-collection": "medical-record-uploads",
        "data-operationType": "insert"
    },
    {
        label: "prescribed drugs",
        value: "prescribed-drug",
        "data-collection": "prescriptions",
        "data-operationType": "insert"
    },
    {
        label: "unlinked category from departments",
        value: "unlinked-category-department",
        "data-collection": "category-departments",
        "data-operationType": "delete"
    }
];

const stockItemEventOptions = stockItemEvents
    .filter((event) => event !== "aggregated-stock-balances")
    .map((value) => {
        const label = value.replace(/-/g, " ");
        const conditions = [{ field: "event", operator: "eqSelf", value }];
        return { label, value, operationTypes: ["insert", "update"], "data-collection": "stockitems", conditions };
    });

export const customOptions = stockItemEventOptions.concat([
    {
        label: "initiated stock transfer",
        value: "initiated-stock-transfer",
        "data-collection": "stockmovements",
        operationTypes: ["insert"],
        conditions: [
            { field: "type", operator: "eq", value: "transfer" },
            { field: "status", operator: "eq", value: "pending" }
        ]
    },
    {
        label: "approved stock transfer request",
        value: "approved-stock-transfer-request",
        "data-collection": "stockmovements",
        operationTypes: ["update"],
        conditions: [
            { field: "type", operator: "eq", value: "transfer" },
            { field: "approvers", operator: "exist" }
        ]
    },
    {
        label: "declined stock transfer request",
        value: "declined-stock-transfer-request",
        "data-collection": "stockmovements",
        operationTypes: ["update"],
        conditions: [
            { field: "type", operator: "eq", value: "transfer" },
            { field: "status", operator: "eq", value: "declined" }
        ]
    },
    {
        label: "updated stock transfer request",
        value: "updated-stock-transfer-request",
        "data-collection": "stockmovements",
        operationTypes: ["update"],
        conditions: [{ field: "stocks", operator: "exists" }]
    },
    {
        label: "requested for stock items",
        value: "requested-for-stock",
        "data-collection": "stockmovements",
        operationTypes: ["insert"],
        conditions: [
            { field: "type", operator: "eq", value: "order" },
            { field: "status", operator: "eq", value: "pending" }
        ]
    },
    {
        label: "accepted order for stock items",
        value: "accepted-stock-order-request",
        "data-collection": "stockmovements",
        operationTypes: ["update"],
        conditions: [
            { field: "type", operator: "eq", value: "order" },
            { field: "status", operator: "eq", value: "accepted" }
        ]
    },
    {
        label: "declined order for stock items",
        value: "declined-stock-order-request",
        "data-collection": "stockmovements",
        operationTypes: ["update"],
        conditions: [
            { field: "type", operator: "eq", value: "order" },
            { field: "status", operator: "eq", value: "declined" }
        ]
    },
    {
        label: "filled discharge form",
        value: "filled-discharge-form",
        "data-collection": "admissions",
        operationTypes: ["update"],
        conditions: [
            {
                field: "discharge",
                operator: "exist"
            }
        ]
    },
    {
        label: "checked out patient",
        value: "checked-out-patient",
        "data-collection": "admissions",
        operationTypes: ["update"],
        conditions: [
            {
                field: "checkout",
                operator: "exist"
            }
        ]
    },
    {
        label: "cancelled admission",
        value: "cancelled-admission",
        "data-collection": "admissions",
        operationTypes: ["update"],
        conditions: [
            {
                field: "cancelled",
                operator: "exist"
            }
        ]
    },
    {
        label: "recorded payment",
        value: "added-payment",
        "data-collection": "bills",
        operationTypes: ["update"],
        conditions: [
            {
                field: "event",
                operator: "eqSelf"
            }
        ]
    },
    {
        label: "dispensed drug",
        value: "dispensed-drug",
        "data-collection": "prescriptions",
        operationTypes: ["update"],
        conditions: [
            {
                field: "lastDispensed",
                operator: "exist"
            }
        ]
    },
    {
        label: "initiated stock recall",
        value: "initiated-stock-recall",
        "data-collection": "stockmovements",
        operationTypes: ["insert"],
        conditions: [{ field: "type", operator: "eq", value: "recall" }]
    },
    {
        label: "processed stock recall",
        value: "processed-stock-recall",
        "data-collection": "stockmovements",
        operationTypes: ["update"],
        conditions: [
            { field: "type", operator: "eq", value: "recall" },
            { field: "status", operator: "eq", value: "processed" }
        ]
    },
    {
        label: "deactivated service",
        value: "deactivated-service",
        "data-collection": "items",
        operationTypes: ["update"],
        conditions: [
            { field: "type", operator: "eq", value: "service" },
            { field: "isActive", operator: "eq", value: false }
        ]
    },
    {
        label: "set reorder level",
        value: "set-reorder-level",
        "data-collection": "items",
        operationTypes: ["update"],
        conditions: [{ field: "reorderLevel", operator: "exist" }]
    },
    {
        label: "converted proforma to invoice",
        value: "converted-proforma",
        "data-collection": "bills",
        operationTypes: ["update"],
        conditions: [{ field: "proforma", operator: "eq", value: false }]
    },
    {
        label: "cancelled bill",
        value: "cancelled-bill",
        "data-collection": "bills",
        operationTypes: ["update"],
        conditions: [{ field: "status", operator: "eq", value: "cancelled" }]
    },
    {
        label: "marked claim as submitted",
        value: "submitted-claims",
        "data-collection": "claims",
        operationTypes: ["update"],
        conditions: [{ field: "submitted", operator: "eq", value: true }]
    },
    {
        label: "recorded payment on claims",
        value: "recorded-claims-payment",
        "data-collection": "claims",
        operationTypes: ["update"],
        conditions: [{ field: "status", operator: "eq", value: "paid" }]
    },
    {
        label: "recorded payment on bill",
        value: "recorded-bill-payment",
        "data-collection": "bills",
        operationTypes: ["update"],
        conditions: [{ field: "payments", operator: "exist" }]
    },
    {
        label: "funded wallet",
        value: "funded-wallet",
        "data-collection": "wallettransactions",
        operationTypes: ["insert"],
        conditions: [
            { field: "type", operator: "eq", value: "credit" },
            { field: "status", operator: "eq", value: "completed" }
        ]
    },
    {
        label: "initiated transfer from wallet",
        value: "initiated-transfer-from-wallet",
        "data-collection": "wallettransactions",
        operationTypes: ["insert"],
        conditions: [
            { field: "type", operator: "eq", value: "debit" },
            { field: "purpose", operator: "eq", value: "transfer" },
            { field: "status", operator: "eq", value: "pending" }
        ]
    },
    {
        label: "initiated transfer from wallet",
        value: "initiated-transfer-from-wallet",
        "data-collection": "wallettransactions",
        operationTypes: ["insert"],
        conditions: [
            { field: "type", operator: "eq", value: "debit" },
            { field: "purpose", operator: "eq", value: "transfer" },
            { field: "status", operator: "eq", value: "pending" }
        ]
    },
    {
        label: "debited wallet(transfer)",
        value: "debited-wallet",
        "data-collection": "wallettransactions",
        operationTypes: ["update"],
        conditions: [
            { field: "type", operator: "eq", value: "debit" },
            { field: "purpose", operator: "eq", value: "transfer" },
            { field: "status", operator: "eq", value: "completed" }
        ]
    },
    {
        label: "credited wallet via transfer",
        value: "credited-wallet",
        "data-collection": "wallettransactions",
        operationTypes: ["update"],
        conditions: [
            { field: "type", operator: "eq", value: "credit" },
            { field: "purpose", operator: "eq", value: "transfer" },
            { field: "status", operator: "eq", value: "completed" }
        ]
    },
    {
        label: "initiated refund",
        value: "initiated-refund",
        "data-collection": "wallettransactions",
        operationTypes: ["insert"],
        conditions: [
            { field: "type", operator: "eq", value: "debit" },
            { field: "purpose", operator: "eq", value: "withdrawal" },
            { field: "status", operator: "eq", value: "pending" }
        ]
    },
    {
        label: "declined refund request",
        value: "declined-refund-request",
        "data-collection": "wallettransactions",
        operationTypes: ["update"],
        conditions: [
            { field: "purpose", operator: "eq", value: "withdrawal" },
            { field: "status", operator: "eq", value: "declined" }
        ]
    },
    {
        label: "approved refund request",
        value: "approved-refund-request",
        "data-collection": "wallettransactions",
        operationTypes: ["update"],
        conditions: [
            { field: "purpose", operator: "eq", value: "withdrawal" },
            { field: "votes", operator: "exist" },
            { field: "status", operator: "ne", value: "declined" }
        ]
    },
    {
        label: "processed refund",
        value: "processed-refund",
        "data-collection": "wallettransactions",
        operationTypes: ["update"],
        conditions: [
            { field: "purpose", operator: "eq", value: "withdrawal" },
            { field: "status", operator: "eq", value: "completed" }
        ]
    }
]);

export const queryOptions = [
    {
        collections: ["admissions", "appointments"],
        label: "Patient (mrn)",
        value: "patient.mrn",
        "data-input": "text"
    },
    {
        collections: ["bills"],
        label: "Patient",
        value: "patient",
        "data-input": "dropdown",
        "data-url": "/patients",
        "dropdown-label": "mrn",
        "dropdown-value": "_id"
    }
];
