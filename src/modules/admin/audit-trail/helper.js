import { actionsToExclude, customOptions, fieldsToExclude, operationTypes } from "admin/audit-trail/constants";
import { getBaseOptionsForAuditTrail } from "app/misc/helper";
import { removePropertiesFromObject, valueIsNonEmpty } from "helpers/utils";
import RedisUtils from "utils/redis";

/**
 * Remove sensitive properties e.g. password hash, pin
 * @param collection
 * @param document
 * @returns {*}
 */
export const removeSensitiveProperties = (collection, document) => {
    const fieldsToBeRemoved = ["_v"].concat(fieldsToExclude[collection]);
    document.snapshot = removePropertiesFromObject(document.snapshot, fieldsToBeRemoved);
    if (document.changes) {
        document.changes = removePropertiesFromObject(document.changes, fieldsToBeRemoved);
    }
    return document;
};
/**
 *
 * @param {Array<{}>} options
 * @param {String} collection
 * @param {String} operationType
 * @returns {{}}
 */
const getOption = (options, collection, operationType) => {
    return options.find((obj) => {
        return obj["data-collection"] === collection && obj["data-operationType"] === operationType;
    });
};

const getCustomOptions = (collection, operationType) => {
    return customOptions.filter((obj) => {
        return obj["data-collection"] === collection && obj.operationTypes.includes(operationType);
    });
};

const determineActionForRegularCollection = (collection, operationType) => {
    const options = getBaseOptionsForAuditTrail();
    const option = getOption(options, collection, operationType);
    return option?.value;
};
const conditionIsFulfilled = (option, payload) => {
    const { field, operator, value } = option.config;
    const payloadValue = payload[field].new;
    if (operator === "exist" && valueIsNonEmpty(payloadValue)) return option;
    if (operator === "eq" && payloadValue === value) return option;
    if (operator === "eqSelf" && payloadValue === option.value) return option;
    if (operator === "ne" && payloadValue !== option.value) return option;
    return false;
};

const determineActionForSuperCollection = (payload, possibleOptions) => {
    const actualOption = possibleOptions.find((option) =>
        option.conditions.every((condition) => conditionIsFulfilled(condition, payload))
    );
    return actualOption?.value;
};

/**
 *
 * @param collection
 * @param operationType
 * @param payload
 * @returns {string}
 */
export const determineAction = (collection, operationType, payload = {}) => {
    const options = getCustomOptions(collection, operationType);
    const action = options?.length
        ? determineActionForSuperCollection(payload, options)
        : determineActionForRegularCollection(collection, operationType);
    return action || `${operationTypes[operationType]}-${collection}`;
};

export const composeFilter = (payload) => {
    const { branchId, parentOrganizationId, startDate, endDate, actions, operator, field, value } = payload;
    const filter = {
        parentOrganizationId,
        action: actions ? { $in: actions } : { $nin: actionsToExclude }
    };
    if (branchId) filter.branchId = branchId;
    if (startDate) filter.createdAt = { $gte: startDate };
    if (endDate) filter.createdAt = { $gte: startDate, $lte: endDate };
    if (operator) filter.operator = operator;
    if (field) filter[`snapshot.${field}`] = { $regex: value, $options: "i" };
    return filter;
};

export const determineOperationTypeForUpdateEvents = (changes) => {
    if (changes.isActive) return "reactivate";
    if (changes.isActive === false) return "deactivate";
    return "update";
};

export const preventChangeStreamDuplicates = async (key) => {
    if (await RedisUtils.get(key)) return;
    RedisUtils.set(key, key, { EX: 3000 });
};
