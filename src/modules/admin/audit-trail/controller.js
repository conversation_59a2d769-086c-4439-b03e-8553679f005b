/* eslint-disable import/no-cycle */
import {
    composeFilter,
    determineAction,
    determineOperationTypeForUpdateEvents,
    preventChangeStreamDuplicates,
    removeSensitiveProperties
} from "admin/audit-trail/helper";
import AuditTrail from "admin/audit-trail/model";
import { FailedLog } from "app/misc/model";
import { paginate } from "base/helper";
import { addToFeed } from "clinic/feed/controller";
import { MEDICAL_RECORD_MODELS } from "helpers/constants";
import { error, success } from "helpers/response";
import { computeDiff, isEnv } from "helpers/utils";
import { model, modelNames, Types } from "mongoose";

const { ObjectId } = Types;

const composeDocForInserts = (payload) => {
    const { fullDocument: snapshot, ns, documentKey, operationType } = payload;
    const action = determineAction(ns.coll, operationType, snapshot);
    const { branchId, parentOrganizationId } = snapshot;
    const operator = snapshot.operator._id;
    return { action, operationType, snapshot, operator, parentOrganizationId, branchId, documentId: documentKey._id };
};

const getPreviousRecord = async (recordId) => {
    return AuditTrail.findOne({ documentId: String(recordId) }, {}, { sort: { createdAt: -1 } }).lean();
};

const composeDocForUpdates = async (payload) => {
    const { documentKey, fullDocument, updateDescription, ns } = payload;
    const { operator, branchId, parentOrganizationId } = fullDocument;
    const previousRecord = await getPreviousRecord(documentKey._id);
    const changes = computeDiff(previousRecord?.snapshot, updateDescription.updatedFields);
    delete changes.updatedAt;
    const operationType = determineOperationTypeForUpdateEvents(changes);
    const action = determineAction(ns.coll, operationType, changes);
    return {
        action,
        operationType,
        snapshot: fullDocument,
        changes,
        operator: operator._id,
        parentOrganizationId,
        branchId,
        documentId: documentKey._id
    };
};

const getOperatorDeviceInfo = async (operator) => {
    const staff = await model("Staff").findOne({ _id: new ObjectId(operator) });
    const user = await model("User").findOne({ email: staff.email });
    return user.device;
};

const composeDoc = async (event) => {
    let doc;
    const { operationType } = event;
    if (operationType === "insert") doc = composeDocForInserts(event);
    else if (operationType === "update") doc = await composeDocForUpdates(event);
    const operatorDevice = await getOperatorDeviceInfo(doc.operator);
    doc = { ...doc, operatorDevice };
    return doc;
};

const getModel = (collectionName) => {
    return modelNames().find((modelName) => model(modelName).collection.name === collectionName);
};

export const logChanges = async (event) => {
    try {
        if (!["insert", "update"].includes(event.operationType)) return; // Record only upsert operations

        const docModel = getModel(event.ns.coll);
        if (event.operationType === "insert" && MEDICAL_RECORD_MODELS.includes(docModel)) {
            await addToFeed({ ...event.fullDocument, docModel });
        }

        let doc = await composeDoc(event);
        doc = removeSensitiveProperties(event.ns.coll, doc);
        await preventChangeStreamDuplicates(event._id._data);
        await AuditTrail.create({ ...doc, coll: event.ns.coll });
    } catch (err) {
        if (isEnv("production")) await FailedLog.create({ changeEvent: event, error: err.message || String(err) });
    }
};

export const fetch = async (req, res) => {
    try {
        const { page } = req.query;
        const { parentOrganizationId } = req.user.currentLocation;
        const filter = composeFilter({ ...req.query, parentOrganizationId });
        const payload = { modelName: "AuditTrail", filter, page, populate: { path: "operator", select: "name email" } };
        const data = await paginate(payload);
        return success(res, 200, data);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetchDocuments = async (req, res) => {
    try {
        const { _id } = req.params;
        const { parentOrganizationId } = req.user.currentLocation;
        const filter = { parentOrganizationId, documentId: _id };
        const payload = {
            modelName: "AuditTrail",
            filter,
            page: 1,
            populate: { path: "operator", select: "name email" }
        };
        const data = await paginate(payload);
        return success(res, 200, data);
    } catch (err) {
        return error(res, 500, err);
    }
};
