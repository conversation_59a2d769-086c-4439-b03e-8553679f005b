### Overview:

This section is for recording user activities on the app. The types of activities we are tracking are: create, update and delete i.e. any changes made to records in the database. We are using [MongoDB's Change Streams](https://www.mongodb.com/docs/manual/changeStreams) for this.

### Database Structure

```json
{
    "action": "action performed",
    "operator": "The id of the operator who performed the action",
    "documentId": "The id of the affected document",
    "snapshot": "The snapshot of the document after the action was performed",
    "changes": "The changes made to the document after the action was performed. Only applicable to update operations",
    "operationType": "The type of operation that was performed e.g. insert, update, delete",
    "createdAt": "Date and time the activity was performed",
    "branchId": "Branch the action was performed in",
    "parentOrganizationId": "The id of the parent company",
    "coll": "The name of the affected collection"
}
```

Note:

-   **Snapshot** MongoDB's change stream does not provide the snapshot of the document before the action was performed. To fix this and provide the full picture i.e. what was there before and what changed, we save the snapshot of the document after insert/update so we can use that as the previous state.

-   **Changes:** This object is the difference between the previous snapshot and the current snapshot. The function responsible (`computeDiff`) for this can be [found here](helper.js)

-   **Delete Operations:** - There is a general function for delete operations (`deleteRecord`) in the base controller. This handles deletion of the record and logging in Audit Trail.

### Log Changes

To log changes, you need to do a few things:

-   Create a change stream and call the `logChanges` function.
    E.g.

```js
const Model = model("Bed", schema);

Model.watch({ fullDocument: "updateLookup" }).on("change", (changeEvent) => logChanges(changeEvent));
```
