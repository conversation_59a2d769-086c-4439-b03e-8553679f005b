import { isAuthorized } from "base/request";
import { Router } from "express";
import { validator } from "helpers/config";
import { fetch, fetchSectionFilters, fetchSections } from "./controller";
import { fetchReportBodySchema, fetchReportQuerySchema } from "./request";

const router = new Router();

router.get("/reports/sections", isAuthorized(["super-view"]), fetchSections);

router.get("/reports/section/:section", isAuthorized(["super-view"]), fetchSectionFilters);

router.post(
    "/reports",
    isAuthorized(["super-view"]),
    validator.query(fetchReportQuerySchema),
    validator.body(fetchReportBodySchema),
    fetch
);

export default router;
