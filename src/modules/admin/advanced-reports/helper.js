import {
    followUpBestcorrected,
    followUpUncorrected,
    PatientSummary,
    patientVisualOutcome,
    surgeryReport
} from "clinic/case-notes/reports/index";
import { medicalReferralReport } from "clinic/referrals/report";
import { anaesthesiaReport } from "clinic/surgeries/reports.js/anaesthesia";
import { vitalReport } from "clinic/vitals/report";
import { paymentReport } from "finance/reports/payments";
import { salesReport } from "finance/reports/sales";
import { walletTransactionReport } from "finance/reports/wallet-transaction";
import { appointmentReport } from "frontdesk/appointments/reports";
import { diagnosisReport, patientReport } from "frontdesk/patients/reports";
import { SPECIALTIES } from "helpers/constants";
import { admissionSummary } from "inpatient/admissions/reports/admissionSummary";
import { operatedPatients } from "inpatient/op-notes/reports/operatedPersons";
import { get as inventoryStock } from "inventory/reports/inventory-stock";
import internalConsumables from "inventory/stock-movements/reports/internalConsumables";
import { stockMovementReport } from "inventory/stock-movements/reports/stock-movement";
import {
    acceptanceReport,
    anaethesiaStatisticReport,
    cataractSurgery,
    dischargeAcuity,
    doctorWise,
    intraOpComplication,
    postOpComplication,
    preOpDischargeDetail,
    preOpDischargeSummary
} from "outpatient/surgery/reports";
import { prescriptionReport } from "prescriptions/reports";

export const sectionToFnMap = [
    { displayType: "basic-table", id: patientReport, name: "Patient", specialties: Object.values(SPECIALTIES) },
    {
        displayType: "basic-table",
        id: prescriptionReport,
        name: "Prescription",
        specialties: Object.values(SPECIALTIES)
    },
    { displayType: "basic-table", id: diagnosisReport, name: "Diagnosis", specialties: Object.values(SPECIALTIES) },
    { displayType: "basic-table", id: appointmentReport, name: "Appointment", specialties: Object.values(SPECIALTIES) },
    { displayType: "basic-table", id: vitalReport, name: "Vitals", specialties: Object.values(SPECIALTIES) },
    {
        displayType: "basic-table",
        id: medicalReferralReport,
        name: "Medical Referral",
        specialties: Object.values(SPECIALTIES)
    },
    { displayType: "basic-table", id: surgeryReport, name: "Advised Surgery", specialties: Object.values(SPECIALTIES) },
    { displayType: "basic-table", id: paymentReport, name: "Bill", specialties: Object.values(SPECIALTIES) },
    {
        displayType: "basic-table",
        id: admissionSummary,
        name: "Admissions Summary",
        specialties: Object.values(SPECIALTIES)
    },
    {
        displayType: "basic-table",
        id: anaesthesiaReport,
        name: "Anaesthesia",
        specialties: [SPECIALTIES.Ophthalmology]
    },
    {
        displayType: "basic-table",
        id: operatedPatients,
        name: "Operated Patients",
        specialties: Object.values(SPECIALTIES)
    },
    {
        displayType: "basic-table",
        id: stockMovementReport,
        name: "Stock Movement",
        specialties: Object.values(SPECIALTIES)
    },
    {
        displayType: "basic-table",
        id: PatientSummary,
        name: "Patients Visit Summary",
        specialties: Object.values(SPECIALTIES)
    },
    {
        displayType: "basic-table",
        id: internalConsumables,
        name: "Internal Consumables",
        specialties: Object.values(SPECIALTIES)
    },
    {
        displayType: "basic-table",
        id: patientVisualOutcome,
        name: "Patient Visual Outcome",
        specialties: Object.values(SPECIALTIES)
    },
    {
        displayType: "basic-table",
        id: inventoryStock,
        name: "Inventory",
        specialties: Object.values(SPECIALTIES)
    },
    {
        displayType: "statistic-table",
        id: anaethesiaStatisticReport,
        name: "Anaethesia Statistics",
        specialties: [SPECIALTIES.Ophthalmology]
    },
    {
        displayType: "statistic-table",
        id: salesReport,
        name: "Product Sales",
        specialties: Object.values(SPECIALTIES)
    },
    {
        displayType: "statistic-table",
        id: cataractSurgery,
        name: "Surgery Count Report",
        specialties: Object.values(SPECIALTIES)
    },
    {
        displayType: "statistic-table",
        id: intraOpComplication,
        name: "IntraOp Complication",
        specialties: [SPECIALTIES.Ophthalmology]
    },
    {
        displayType: "statistic-table",
        id: postOpComplication,
        name: "PostOp Complication",
        specialties: [SPECIALTIES.Ophthalmology]
    },
    {
        displayType: "statistic-table",
        id: dischargeAcuity,
        name: "Discharge Acuity",
        specialties: Object.values(SPECIALTIES)
    },
    {
        displayType: "statistic-table",
        id: acceptanceReport,
        name: "Acceptance Rate",
        specialties: [SPECIALTIES.Ophthalmology]
    },
    {
        displayType: "statistic-table",
        id: followUpUncorrected,
        name: "Uncorrected 8 to 90 days",
        specialties: [SPECIALTIES.Ophthalmology]
    },
    {
        displayType: "statistic-table",
        id: followUpBestcorrected,
        name: "Best Corrected 8 to 90 days",
        specialties: [SPECIALTIES.Ophthalmology]
    },
    {
        displayType: "statistic-table",
        id: preOpDischargeSummary,
        name: "Pre Op Discharge Summary Best Corrected",
        specialties: [SPECIALTIES.Ophthalmology]
    },
    {
        displayType: "statistic-table",
        id: preOpDischargeDetail,
        name: "Pre Op Discharge Details Best Corrected",
        specialties: [SPECIALTIES.Ophthalmology]
    },
    {
        displayType: "statistic-table",
        id: doctorWise,
        name: "Doctorwise Pre Op Discharge",
        specialties: [SPECIALTIES.Ophthalmology]
    },
    {
        displayType: "basic-table",
        id: walletTransactionReport,
        name: "Wallet Transaction",
        specialties: Object.values(SPECIALTIES)
    }
];

export const handleReportSections = async (section, query, req) => {
    const fn = sectionToFnMap.find((sectionFn) => sectionFn.name === section);
    const data = await fn.id(query, req);
    return {
        displayType: fn.displayType,
        ...data
    };
};
