import { SEARCH_FILTERS } from "admin/advanced-reports/constant";
import { handleReportSections, sectionToFnMap } from "admin/advanced-reports/helper";
import { addDays } from "date-fns";
import { error, success } from "helpers/response";
import _ from "lodash";

export const fetchSections = async (req, res) => {
    const { specialties } = req.user.currentLocation;
    try {
        const sections = sectionToFnMap.reduce((acc, curr) => {
            const specialtyMatched = _.intersection(specialties, curr.specialties);
            if (specialtyMatched.length) acc.push(curr.name);
            return acc;
        }, []);
        return success(res, 200, sections);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetchSectionFilters = async (req, res) => {
    try {
        const filters = SEARCH_FILTERS.filter((x) => x.section.includes(req.params.section));
        return success(res, 200, filters);
    } catch (err) {
        return error(res, 500, err);
    }
};

const composeBaseFilter = (req) => {
    const filter = { parentOrganizationId: req.user.currentLocation.parentOrganizationId };
    Object.entries(req.body.filters).forEach((entry) => {
        const [key, obj] = entry;
        if ([null, undefined, ""].includes(obj.value)) return;
        if (Array.isArray(obj.value)) filter[key] = { $in: obj.value };
        else if (obj.fieldType == "date_range") {
            const dateRange = obj.value.split(";");
            filter[key] = { $gte: new Date(dateRange[0]), $lte: addDays(new Date(dateRange[1]), 1) };
        } else filter[key] = obj.value;
    });

    if (filter.branchId && filter.branchId == "all") delete filter.branchId;
    return filter;
};

export const fetch = async (req, res) => {
    try {
        const filter = composeBaseFilter(req);
        const data = await handleReportSections(req.query.section, filter, req);
        return success(res, 200, data);
    } catch (err) {
        return error(res, 500, err);
    }
};
