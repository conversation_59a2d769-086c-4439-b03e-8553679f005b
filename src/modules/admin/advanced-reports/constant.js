export const SEARCH_FILTERS = [
    {
        field_name: "operator._id",
        label_name: "Operator",
        section: [
            "Appointment",
            "Prescription",
            "Bill",
            "Vitals",
            "Surgery",
            "Procedure",
            "Patient",
            "Operated Patients",
            "Anesthesia",
            "Claims",
            "Advised Surgery"
        ],
        type: "select",
        url: "/staff",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "updatedBy._id",
        label_name: "Operator",
        section: ["Anaesthesia"],
        type: "select",
        url: "/staff",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "consultant._id",
        label_name: "Consultant",
        section: [
            "Appointment",
            "Medical Referral",
            "Advised Surgery",
            "Claims",
            "Patients Visit Summary",
            "Operated Patients",
            "Doctors Performance",
            "Doctorwise Pre Op Discharge"
        ],
        type: "select",
        url: "/staff",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "specialty",
        label_name: "Specialty",
        section: [
            "Vitals",
            "Diagnosis",
            "Appointment",
            "Advised Surgery",
            "Medical Referral",
            "Admissions Summary",
            "Operated Patients",
            "Anaesthesia",
            "Outpatient Summary",
            "Acceptance Rate",
            "Surgery Count Report",
            "PostOp Complication"
        ],
        type: "select",
        url: "/static-data?value=specialty",
        static: false,
        select_value: "value",
        select_label: "name"
    },
    {
        field_name: "internalRefferal.specialty",
        label_name: "Specialty",
        section: ["Medical Refferal"],
        type: "select",
        url: "/static-data?value=specialty",
        static: true
    },
    {
        field_name: "month",
        label_name: "Month",
        section: ["Advice vs Accepted"],
        type: "select",
        url: "/static-data?value=months",
        select_value: "value",
        select_label: "name",
        static: true
    },
    {
        field_name: "year",
        label_name: "Year",
        section: ["Advice vs Accepted"],
        type: "select",
        url: "/static-data?value=years",
        static: true
    },
    {
        field_name: "specialty",
        label_name: "Specialty",
        section: ["Advice vs Accepted", "Patients Visit Summary", "Acceptance Rate"],
        type: "select",
        url: `/branches/:_id/specialties`,
        select_value: "value",
        select_label: "name",
        static: false
    },
    {
        field_name: "createdAt",
        label_name: "Date of Entry",
        section: [
            "Patient",
            "Appointment",
            "Prescription",
            "Patients Visit Summary",
            "Operated Patients",
            "Medical Referral",
            "Internal Consumables",
            "Anaesthesia",
            "Diagnosis",
            "Procedure",
            "Surgery",
            "Vitals",
            "Claims",
            "Stock Movement",
            "Anaesthesia Rate",
            "Outpatient Summary",
            "Cataract Surgery",
            "Surgery Count Report",
            "IntraOp Complication",
            "PostOp Complication",
            "Discharge Acuity",
            "Acceptance Rate",
            "Patient Visual Outcome",
            "Inventory",
            "Uncorrected 8 to 90 days",
            "Best Corrected 8 to 90 days",
            "Advised Surgery",
            "Pre Op Discharge Summary Best Corrected",
            "Pre Op Discharge Details Best Corrected",
            "Doctors Performance",
            "Doctorwise Pre Op Discharge",
            "Wallet Transaction",
            "Product Sales"
        ],
        type: "date_range"
    },
    {
        field_name: "createdAt",
        label_name: "Billed Date",
        section: ["Bill"],
        type: "date_range"
    },
    {
        field_name: "checkInDate",
        label_name: "Admission Date",
        section: ["Admissions Summary"],
        type: "date_range"
    },
    {
        field_name: "dateOfBirth",
        label_name: "Date of Birth",
        section: ["Patient"],
        type: "date_range"
    },
    {
        field_name: "patient.dateOfBirth",
        label_name: "Date of Birth",
        section: ["Appointment", "Prescription"],
        type: "date_range"
    },
    {
        field_name: "appointmentDate",
        label_name: "Appointment Date",
        section: ["Appointment"],
        type: "date_range"
    },
    {
        field_name: "appointmentPurpose._id",
        label_name: "Appointment Purpose",
        section: ["Appointment", "Doctors Performance"],
        type: "select",
        url: "/appointment-purposes",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "admissionStatus",
        label_name: "Admission Status",
        section: ["Admissions Summary"],
        type: "select",
        url: "/admissions/status",
        select_value: "value",
        select_label: "label",
        static: false
    },
    {
        field_name: "gender",
        label_name: "Gender",
        section: ["Patient"],
        type: "select",
        url: "/static-data?value=gender",
        static: true
    },
    {
        field_name: "patient.gender",
        label_name: "Gender",
        section: ["Appointment", "Prescription"],
        type: "select",
        url: "/static-data?value=gender",
        static: true
    },
    {
        field_name: "country",
        label_name: "Country",
        section: ["Patient"],
        type: "select",
        url: "/static-data?value=countries",
        static: true
    },
    {
        field_name: "patientPlan._id",
        label_name: "Patient Plan",
        section: ["Patient", "Claims"],
        type: "select",
        url: "/plans",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "patient.patientPlan._id",
        label_name: "Patient Plan",
        section: ["Appointment"],
        type: "select",
        url: "/plans",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "religion",
        label_name: "Religion",
        section: ["Patient"],
        type: "select",
        url: "/static-data?value=religion",
        static: true
    },
    {
        field_name: "visitType",
        label_name: "Visit Type",
        section: ["Patients Visit Summary"],
        type: "select",
        url: "/static-data?value=visitType",
        static: true
    },
    {
        field_name: "section",
        label_name: "Visit Type",
        section: ["Doctors Performance"],
        type: "select",
        url: "/static-data?value=section",
        static: true
    },
    {
        field_name: "lastVisitDate",
        label_name: "Last Visited",
        section: ["Patient"],
        type: "date_range"
    },
    {
        field_name: "patient.lastVisitDate",
        label_name: "Last Visited",
        section: ["Appointment"],
        type: "date_range"
    },
    {
        field_name: "items._id",
        label_name: "Product Name",
        section: [],
        type: "search",
        url: "/locations/:_id/items?type=service&name=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "operator._id",
        label_name: "Operator",
        section: ["Patients Visit Summary", "Wallet Transaction"],
        type: "search",
        url: "/staff?location=&name=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "items._id",
        label_name: "Product Name",
        section: ["Bill", "Claims"],
        type: "search",
        url: "/locations/:_id/items?type=product&name=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "service._id",
        label_name: "Services",
        section: ["Claims"],
        type: "search",
        url: "/locations/:_id/items?type=service&name=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "serviceId",
        label_name: "Services",
        section: ["Bill"],
        type: "search",
        url: "/locations/:_id/items?type=service&name=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "service._id",
        label_name: "Surgery/Procedure",
        section: ["Doctors Performance"],
        type: "search",
        url: "/items?type=service&name=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "sponsors._id",
        label_name: "HMO/Company",
        section: ["Patient", "Bill"],
        type: "select",
        url: "/sponsors",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "patient.sponsors._id",
        label_name: "HMO/Company",
        section: ["Appointment"],
        type: "select",
        url: "/sponsors",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "allergies",
        label_name: "Allergies",
        section: ["Patient"],
        type: "text"
    },
    {
        field_name: "patient.allergies",
        label_name: "Allergies",
        section: ["Prescription", "Appointment"],
        type: "text"
    },
    {
        field_name: "patient._id",
        label_name: "Patient's Name",
        section: [
            "Appointment",
            "Patients Visit Summary",
            "Patient",
            "Admissions Summary",
            "Operated Patients",
            "Medical Referral",
            "Diagnosis",
            "Anesthesia",
            "Vitals",
            "Surgery",
            "Procedure",
            "Internal Consumables"
        ],
        type: "search",
        url: "/patients?wildCardSearch=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "patient",
        label_name: "Patient's Name",
        section: ["Prescription", "Bill"],
        type: "search",
        url: "/patients?wildCardSearch=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "from",
        label_name: "Patient's Name",
        section: ["Wallet Transaction"],
        type: "search",
        url: "/patients?wildCardSearch=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "mrn",
        label_name: "MRN",
        section: ["Vitals", "Advised Surgery", "Patients Visit Summary", "Patient"],
        type: "search",
        url: "/patients?mrn=",
        select_value: "mrn",
        select_label: "mrn"
    },
    {
        field_name: "patient.mrn",
        label_name: "MRN",
        section: ["Appointment", "Prescription"],
        type: "search",
        url: "/patients?mrn=",
        select_value: "mrn",
        select_label: "mrn"
    },
    {
        field_name: "section",
        label_name: "Patient Type",
        section: ["Diagnosis", "Vitals"],
        type: "select",
        url: "/static-data?value=patientType",
        static: true
    },
    {
        field_name: "referral",
        label_name: "Referral",
        section: ["Patient", "Appointment"],
        type: "select",
        url: "/static-data?value=referral",
        static: true
    },
    {
        field_name: "purpose",
        label_name: "Consumable Types",
        section: ["Internal Consumables"],
        type: "select",
        url: "/static-data?value=purpose",
        static: true
    },
    {
        field_name: "billId",
        label_name: "Invoice No",
        section: ["Claims"],
        type: "text"
    },
    {
        field_name: "categoryId",
        label_name: "Category",
        section: [
            "Anaesthesia Rate",
            "Patient Visual Outcome",
            "Pre Op Discharge Summary Best Corrected",
            "Pre Op Discharge Details Best Corrected",
            "Internal Consumables",
            "Doctorwise Pre Op Discharge",
            "Stock Movement"
        ],
        type: "select",
        url: "/categories",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "items.categories._id",
        label_name: "Category",
        section: ["Bill", "Product Sales"],
        type: "select",
        url: "/categories/",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "stocks.categories._id",
        label_name: "Category",
        section: ["Stock Movement"],
        type: "select",
        url: "/categories",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "stocks.supplier._id",
        label_name: "Supplier",
        section: ["Stock Movement"],
        type: "select",
        url: "/suppliers",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "supplier._id",
        label_name: "Supplier",
        section: ["Inventory"],
        type: "select",
        url: "/suppliers",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "payments.method",
        label_name: "Payment Method",
        section: ["Bill"],
        type: "select",
        url: "/static-data?value=paymentMethod",
        static: false,
        select_value: "value",
        select_label: "name"
    },
    {
        field_name: "status",
        label_name: "Payment Status",
        section: ["Bill"],
        type: "select",
        url: "/static-data?value=paymentStatus",
        static: false,
        select_value: "value",
        select_label: "name"
    },
    {
        field_name: "transactionType",
        label_name: "Transaction Type",
        section: ["Bill"],
        type: "select",
        url: "/static-data?value=transactionType",
        static: false,
        select_value: "value",
        select_label: "name"
    },
    {
        field_name: "payments.provider",
        label_name: "Banks",
        section: ["Bill"],
        type: "select",
        url: "/banks",
        select_value: "name",
        select_label: "name",
        static: false
    },
    {
        field_name: "ward._id",
        label_name: "Ward",
        section: ["Admissions Summary"],
        type: "select",
        url: `/branches/:_id/wards`,
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "itemId",
        label_name: "Item",
        section: ["Internal Consumables"],
        type: "search",
        url: "/locations/:_id/items?type=product&name=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "to._id",
        label_name: "Department",
        section: ["Internal Consumables"],
        type: "search",
        url: "/departments?search=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "anaesthesia.name",
        label_name: "Anaesthesia",
        section: ["Anaesthesia"],
        type: "search",
        url: "/test-standards/values?value=",
        select_value: "name",
        select_label: "name",
        static: false
    },
    {
        field_name: "anaesthesiaComplications.name",
        label_name: "Anesthesia Complication",
        section: ["Anesthesia"],
        type: "search",
        url: "/test-standards/values?value=",
        select_value: "name",
        select_label: "name",
        static: false
    },
    {
        field_name: "surgery.item._id",
        label_name: "Surgery",
        section: ["Surgery", "Patients Visit Summary"],
        type: "search",
        url: "/locations/:_id/items?type=service&name=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "pocedure.item._id",
        label_name: "Non surgical procedure",
        section: ["Procedure"],
        type: "search",
        url: "/items?type=service&name=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "pocedure.item._id",
        label_name: "Procedure",
        section: ["Patients Visit Summary"],
        type: "search",
        url: "/locations/:_id/items?type=service&name=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "claimStatus",
        label_name: "Claim Status",
        section: ["Bill"],
        type: "select",
        url: "/static-data?value=claimStatus",
        static: true
    },
    {
        field_name: "productStatus",
        label_name: "Product Status",
        section: ["Inventory"],
        type: "select",
        url: "/static-data?value=productStatus",
        select_value: "value",
        select_label: "name",
        static: false
    },
    {
        field_name: "status",
        label_name: "Status",
        section: ["Wallet Transaction"],
        type: "select",
        url: "/static-data?value=walletTransactionStatus",
        select_value: "value",
        select_label: "name",
        static: false
    },
    {
        field_name: "purpose",
        label_name: "Purpose",
        section: ["Wallet Transaction"],
        type: "select",
        url: "/static-data?value=walletTransactionPurpose",
        select_value: "value",
        select_label: "name",
        static: false
    },
    {
        field_name: "status",
        label_name: "Appointment Status",
        section: ["Appointment"],
        type: "select",
        url: "/static-data?value=appointmentStatus",
        select_value: "value",
        select_label: "name",
        static: false
    },
    {
        field_name: "status",
        label_name: "Stock Status",
        section: ["Inventory"],
        type: "select",
        url: "/static-data?value=stockStatus",
        select_value: "value",
        select_label: "name",
        static: false
    },
    {
        field_name: "pointOfSaleClassification",
        label_name: "Inventory Classification",
        section: ["Inventory"],
        type: "select",
        url: "/static-data?value=pointOfSaleClassification",
        select_value: "value",
        select_label: "name",
        static: false
    },
    {
        field_name: "stocks.batchId",
        label_name: "Batch Number",
        section: ["Stock Movement"],
        type: "text"
    },
    {
        field_name: "stocks.item._id",
        label_name: "Item/Product",
        section: ["Stock Movement"],
        type: "search",
        url: "/items?type=product&name=",
        select_value: "_id",
        select_label: "name",
        static: false
    },
    {
        field_name: "type",
        label_name: "Reason",
        section: ["Stock Movement"],
        type: "select",
        url: "/static-data?value=stockMovementTypes",
        static: true
    },
    {
        field_name: "status",
        label_name: "Status",
        section: ["Stock Movement"],
        type: "select",
        url: "/static-data?value=stockMovementStatus",
        static: true
    },
    {
        field_name: "payments.date",
        label_name: "Payment Date",
        section: ["Bill"],
        type: "date_range"
    },
    {
        field_name: "patientAccountType",
        label_name: "Account Type",
        section: ["Patient"],
        type: "select",
        url: "/static-data?value=patientAccountType",
        static: true
    }
];
