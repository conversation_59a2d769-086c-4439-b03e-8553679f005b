import { endOfMonth, startOfMonth } from "date-fns";
import { Types } from "mongoose";
import { error, success } from "helpers/response";
import Bill from "finance/bills/model";
import Claim from "finance/claims/model";
import Prescription from "pharmacy/prescriptions/model";
import StockMovement from "inventory/stock-movements/model";
import { totalRevenueSummary } from "admin/financial-report-summaries/helper";

const { ObjectId } = Types;

export const privatePatientRevenueSummary = async (req, res) => {
    const { branchId } = req.query;
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        const bill = await Bill.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    ...(branchId && { branchId }),
                    parentOrganizationId,
                    status: { $in: ["owing", "paid"] },
                    "claims.0": { $exists: false }
                }
            },
            {
                $group: {
                    _id: { status: "$status", branchId: "$branchId" },
                    totalPatient: { $sum: 1 },
                    totalRevenue: {
                        $sum: { $cond: [{ $eq: ["$status", "paid"] }, "$totalPaid", 0] }
                    },
                    amountOwned: {
                        $sum: { $cond: [{ $eq: ["$status", "owing"] }, "$balance", 0] }
                    }
                }
            }
        ]);
        return success(res, 200, bill);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const privatePatientRevenueWeeklySummary = async (req, res) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        const { branchId } = req.query;
        const bill = await Bill.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    status: { $in: ["owing", "paid"] },
                    parentOrganizationId,
                    ...(branchId && { branchId }),
                    "claims.0": { $exists: false }
                }
            },
            {
                $project: {
                    status: 1,
                    branchId: 1,
                    weekOfMonth: { $add: [{ $floor: { $divide: [{ $dayOfMonth: "$updatedAt" }, 7] } }, 1] },
                    totalPaid: 1
                }
            },
            {
                $group: {
                    _id: {
                        status: "$status",
                        week: "$weekOfMonth"
                    },
                    totalPatient: { $sum: 1 },
                    totalRevenue: {
                        $sum: { $cond: [{ $eq: ["$status", "paid"] }, "$totalPaid", 0] }
                    },
                    amountOwned: {
                        $sum: { $cond: [{ $eq: ["$status", "owing"] }, "$balance", 0] }
                    }
                }
            },
            {
                $project: {
                    week: "$_id.week",
                    status: "$_id.status",
                    _id: 0,
                    totalPatient: 1,
                    totalRevenue: 1,
                    amountOwned: 1
                }
            },
            {
                $sort: { week: 1 }
            }
        ]);
        return success(res, 200, bill);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const privateoutPatientRevenueSummary = async (req, res) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        const { branchId } = req.query;
        const bill = await Bill.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    parentOrganizationId,
                    ...(branchId && { branchId }),
                    status: "paid",
                    "claims.0": { $exists: false },
                    "items.source.name": { $in: ["outpatient", "Appointment", "Patient", "prescription"] }
                }
            },
            {
                $unwind: "$items"
            },
            {
                $project: {
                    patient: 1,
                    "items.source.name": 1,
                    totalOutPatientRevenue: {
                        $sum: {
                            $cond: [
                                {
                                    $in: [
                                        "$items.source.name",
                                        ["outpatient", "Appointment", "Patient", "prescription"]
                                    ]
                                },
                                "$items.total",
                                0
                            ]
                        }
                    },
                    weekOfMonth: { $add: [{ $floor: { $divide: [{ $dayOfMonth: "$updatedAt" }, 7] } }, 1] }
                }
            },
            {
                $group: {
                    _id: { week: "$weekOfMonth" },
                    totalRevenue: { $sum: "$totalOutPatientRevenue" },
                    totalPatients: { $addToSet: { patient: "$patient" } }
                }
            },
            {
                $project: {
                    week: "$_id.week",
                    _id: 0,
                    totalPatients: { $size: "$totalPatients" },
                    totalRevenue: 1
                }
            },
            {
                $sort: { week: 1 }
            }
        ]);
        return success(res, 200, bill);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const totalOutPatientRevenueSummary = async (req, res) => {
    try {
        const result = await totalRevenueSummary(req, res, ["outpatient", "Appointment", "Patient", "prescription"]);
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const totalInPatientRevenueSummary = async (req, res) => {
    try {
        const result = await totalRevenueSummary(req, res, ["inpatient", "admission"]);
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const hmoInPatientRevenueSummary = async (req, res) => {
    try {
        const { branchId } = req.query;
        const { parentOrganizationId } = req.user.currentLocation;
        const bill = await Claim.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    ...(branchId && { branch: new ObjectId(branchId) }),
                    parentOrganizationId,
                    status: "paid"
                }
            },
            {
                $unwind: "$items"
            },
            {
                $lookup: {
                    from: "bills",
                    let: { billItemId: "$items.billItemId" },
                    pipeline: [
                        { $unwind: "$items" },
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$$billItemId", "$items.billItemId"] },
                                        {
                                            $in: ["$items.source.name", ["inpatient", "admission"]]
                                        }
                                    ]
                                }
                            }
                        }
                    ],
                    as: "billItems"
                }
            },
            {
                $unwind: "$billItems"
            },
            {
                $project: {
                    patient: 1,
                    updatedAt: 1,
                    totalRevenue: {
                        $sum: "$billItems.items.total"
                    },
                    weekOfMonth: { $add: [{ $floor: { $divide: [{ $dayOfMonth: "$updatedAt" }, 7] } }, 1] }
                }
            },
            {
                $group: {
                    _id: { week: "$weekOfMonth" },
                    totalRevenue: { $sum: "$totalRevenue" }
                }
            },
            {
                $project: {
                    week: "$_id.week",
                    _id: 0,
                    totalRevenue: 1
                }
            },
            {
                $sort: { week: 1 }
            }
        ]);
        return success(res, 200, bill);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const inPatientMedicalRevenueSummary = async (req, res) => {
    try {
        const { branchId } = req.query;
        const { parentOrganizationId } = req.user.currentLocation;
        const bill = await Prescription.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    parentOrganizationId,
                    ...(branchId && { branchId }),
                    status: "paid",
                    "section.name": "inpatient"
                }
            },
            {
                $unwind: "$items"
            },
            {
                $lookup: {
                    from: "items",
                    localField: "items.drug",
                    foreignField: "_id",
                    as: "prescriptionItems"
                }
            },
            {
                $unwind: "$prescriptionItems"
            },
            {
                $project: {
                    updatedAt: 1,
                    _id: 0,
                    "prescriptionItems.name": 1,
                    totalRevenue: {
                        $sum: "$items.totalPrice"
                    }
                }
            },
            {
                $group: {
                    _id: {
                        item: "$prescriptionItems.name"
                    },
                    totalRevenue: { $sum: "$totalRevenue" }
                }
            },
            {
                $project: {
                    item: "$_id.item",
                    _id: 0,
                    totalRevenue: 1
                }
            }
        ]);
        return success(res, 200, bill);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const outPatientMedicalRevenueSummary = async (req, res) => {
    try {
        const { branchId } = req.query;
        const { parentOrganizationId } = req.user.currentLocation;
        const bill = await Prescription.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    parentOrganizationId,
                    ...(branchId && { branchId }),
                    status: "paid",
                    "section.name": "outpatient"
                }
            },
            {
                $unwind: "$items"
            },
            {
                $lookup: {
                    from: "items",
                    localField: "items.drug",
                    foreignField: "_id",
                    as: "prescriptionItems"
                }
            },
            {
                $unwind: "$prescriptionItems"
            },
            {
                $project: {
                    updatedAt: 1,
                    _id: 0,
                    "prescriptionItems.name": 1,
                    totalRevenue: {
                        $sum: "$items.totalPrice"
                    }
                }
            },
            {
                $group: {
                    _id: {
                        item: "$prescriptionItems.name"
                    },
                    totalRevenue: { $sum: "$totalRevenue" }
                }
            },
            {
                $project: {
                    item: "$_id.item",
                    _id: 0,
                    totalRevenue: 1
                }
            }
        ]);
        return success(res, 200, bill);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const serviceRevenueSummary = async (req, res) => {
    try {
        const { branchId } = req.query;

        const { parentOrganizationId } = req.user.currentLocation;
        const bill = await Bill.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    parentOrganizationId,
                    ...(branchId && { branchId }),
                    status: "paid",
                    "items.source.name": { $in: ["Appointment", "labs"] }
                }
            },
            {
                $unwind: "$items"
            },
            {
                $project: {
                    patient: 1,
                    "items.source.name": 1,
                    "items.name": 1,
                    updatedAt: 1,
                    totalRevenue: {
                        $sum: {
                            $cond: [
                                {
                                    $in: ["$items.source.name", ["Appointment", "labs"]]
                                },
                                "$items.total",
                                0
                            ]
                        }
                    }
                }
            },
            {
                $group: {
                    _id: {
                        item: "$items.name"
                    },
                    totalRevenue: { $sum: "$totalRevenue" },
                    totalPatients: { $addToSet: { patient: "$patient" } }
                }
            },
            {
                $project: {
                    service: "$_id.item",
                    _id: 0,
                    totalPatients: { $size: "$totalPatients" },
                    totalRevenue: 1
                }
            }
        ]);
        return success(res, 200, bill);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const categoryRevenueSummary = async (req, res) => {
    try {
        const { branchId } = req.query;

        const { parentOrganizationId } = req.user.currentLocation;
        const bill = await Bill.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    parentOrganizationId,
                    ...(branchId && { branchId }),
                    status: "paid"
                }
            },
            {
                $unwind: "$items"
            },
            {
                $lookup: {
                    from: "items",
                    localField: "items.name",
                    foreignField: "name",
                    as: "categoryItems"
                }
            },
            {
                $unwind: "$categoryItems"
            },
            {
                $project: {
                    updatedAt: 1,
                    _id: 0,
                    "categoryItems.category.name": 1,
                    totalRevenue: {
                        $sum: "$totalPaid"
                    }
                }
            },
            {
                $group: {
                    _id: {
                        item: "$categoryItems.category.name"
                    },
                    totalRevenue: { $sum: "$totalRevenue" }
                }
            },
            {
                $project: {
                    category: "$_id.item",
                    _id: 0,
                    totalRevenue: 1
                }
            }
        ]);
        return success(res, 200, bill);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const sponsorTopEarnings = async (req, res) => {
    try {
        const { branchId } = req.query;
        const { parentOrganizationId } = req.user.currentLocation;
        const claim = await Claim.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    ...(branchId && { branch: new ObjectId(branchId) }),
                    parentOrganizationId,
                    status: "paid"
                }
            },
            {
                $unwind: "$items"
            },
            {
                $lookup: {
                    let: { sponsorObjId: { $toObjectId: "$sponsor" } },
                    from: "sponsors",
                    pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$sponsorObjId"] } } }, { $project: { name: 1 } }],
                    as: "sponsorDetails"
                }
            },
            {
                $project: {
                    items: 1,
                    totalPaid: 1,
                    updatedAt: 1,
                    sponsorDetails: 1
                }
            },
            {
                $group: {
                    _id: { sponsorName: "$sponsorDetails.name" },
                    earnings: { $sum: "$totalPaid" }
                }
            },
            {
                $project: {
                    sponsor: "$_id.sponsorName",
                    _id: 0,
                    earnings: 1
                }
            },
            {
                $unwind: "$sponsor"
            },
            {
                $sort: { earnings: -1 }
            }
        ]);
        return success(res, 200, claim);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const serviceTopEarnings = async (req, res) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        const { branchId } = req.query;
        const bill = await Bill.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    parentOrganizationId,
                    ...(branchId && { branchId }),
                    status: "paid",
                    "items.source.name": { $in: ["Appointment", "labs"] }
                }
            },
            {
                $unwind: "$items"
            },
            {
                $project: {
                    patient: 1,
                    "items.source.name": 1,
                    "items.name": 1,
                    updatedAt: 1,
                    totalRevenue: {
                        $sum: {
                            $cond: [
                                {
                                    $in: ["$items.source.name", ["Appointment", "labs"]]
                                },
                                "$items.total",
                                0
                            ]
                        }
                    }
                }
            },
            {
                $group: {
                    _id: {
                        item: "$items.name"
                    },
                    totalRevenue: { $sum: "$totalRevenue" }
                }
            },
            {
                $project: {
                    service: "$_id.item",
                    _id: 0,
                    totalRevenue: 1
                }
            },
            {
                $sort: { totalRevenue: -1 }
            }
        ]);
        return success(res, 200, bill);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const sponsorManagement = async (req, res) => {
    try {
        const { branchId } = req.query;
        const { parentOrganizationId } = req.user.currentLocation;
        const bill = await Claim.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    parentOrganizationId,
                    ...(branchId && { branch: new ObjectId(branchId) }),
                    status: { $in: ["paid", "owing"] }
                }
            },
            {
                $lookup: {
                    let: { sponsorObjId: { $toObjectId: "$sponsor" } },
                    from: "sponsors",
                    pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$sponsorObjId"] } } }, { $project: { type: 1 } }],
                    as: "sponsorDetails"
                }
            },
            {
                $project: {
                    status: 1,
                    amount: 1,
                    updatedAt: 1,
                    sponsorDetails: 1,
                    weekOfMonth: { $add: [{ $floor: { $divide: [{ $dayOfMonth: "$updatedAt" }, 7] } }, 1] }
                }
            },
            {
                $group: {
                    _id: { week: "$weekOfMonth", status: "$status", sponsorDetails: "$sponsorDetails.type" },
                    totalAmount: { $sum: "$amount" }
                }
            },
            {
                $project: {
                    week: "$_id.week",
                    status: "$_id.status",
                    sponsorType: "$_id.sponsorDetails",
                    _id: 0,
                    totalAmount: 1
                }
            },
            {
                $unwind: "$sponsorType"
            },
            {
                $sort: { week: 1 }
            }
        ]);
        return success(res, 200, bill);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const operationalCost = async (req, res) => {
    try {
        const { branchId } = req.query;
        const { parentOrganizationId } = req.user.currentLocation;
        const stockMovement = await StockMovement.aggregate([
            {
                $match: {
                    updatedAt: {
                        $gte: startOfMonth(new Date(req.query.date)),
                        $lte: endOfMonth(new Date(req.query.date))
                    },
                    ...(branchId && { branch: new ObjectId(req.query.branchId) }),
                    parentOrganizationId,
                    type: "consumables",
                    "to.name": req.query.department
                }
            },
            {
                $unwind: "$stocks"
            },
            {
                $project: {
                    patient: 1,
                    updatedAt: 1,
                    "to.name": 1,
                    totalOperationalCost: {
                        $multiply: ["$stocks.costPrice", "$stocks.quantity"]
                    },
                    weekOfMonth: { $add: [{ $floor: { $divide: [{ $dayOfMonth: "$updatedAt" }, 7] } }, 1] }
                }
            },
            {
                $group: {
                    _id: { week: "$weekOfMonth", department: "$to.name" },
                    totalOperationalCost: { $sum: "$totalOperationalCost" }
                }
            },
            {
                $project: {
                    week: "$_id.week",
                    department: "$_id.department",
                    _id: 0,
                    totalOperationalCost: 1
                }
            },
            {
                $sort: { week: 1 }
            }
        ]);
        return success(res, 200, stockMovement);
    } catch (err) {
        return error(res, 500, err);
    }
};
