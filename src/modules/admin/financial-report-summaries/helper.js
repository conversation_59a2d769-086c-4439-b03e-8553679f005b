import { endOfMonth, startOfMonth } from "date-fns";
import Bill from "finance/bills/model";
import Claim from "finance/claims/model";

export const totalRevenueSummary = async (req, _res, filter) => {
    const { parentOrganizationId } = req.user.currentLocation;
    const { branchId } = req.query;
    const claims = await Claim.aggregate([
        {
            $match: {
                updatedAt: {
                    $gte: startOfMonth(new Date(req.query.date)),
                    $lte: endOfMonth(new Date(req.query.date))
                },
                parentOrganizationId,
                ...(branchId && { branchId }),
                status: "paid"
            }
        },
        {
            $unwind: "$items"
        },
        {
            $lookup: {
                from: "bills",
                let: { billItemId: "$items.billItemId" },
                pipeline: [
                    { $unwind: "$items" },
                    {
                        $match: {
                            $expr: {
                                $and: [
                                    { $eq: ["$$billItemId", "$items.billItemId"] },
                                    {
                                        $in: ["$items.source.name", filter]
                                    }
                                ]
                            }
                        }
                    }
                ],
                as: "billItems"
            }
        },
        {
            $unwind: "$billItems"
        },
        {
            $lookup: {
                let: { sponsorObjId: { $toObjectId: "$sponsor" } },
                from: "sponsors",
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $and: [
                                    { $eq: ["$_id", "$$sponsorObjId"] },
                                    {
                                        $in: ["$type", ["hmo", "company"]]
                                    }
                                ]
                            }
                        }
                    },
                    { $project: { name: 1, type: 1 } }
                ],
                as: "sponsorDetails"
            }
        },
        {
            $project: {
                _id: 1,
                sponsorDetails: 1,
                totalRevenue: {
                    $sum: "$billItems.totalPaid"
                }
            }
        },
        {
            $unwind: "$sponsorDetails"
        },
        {
            $group: {
                _id: { sponsorDetails: "$sponsorDetails.type" },
                totalRevenue: { $sum: "$totalRevenue" }
            }
        },
        {
            $project: {
                hmoType: "$_id.sponsorDetails",
                _id: 0,
                totalRevenue: 1
            }
        }
    ]);

    const bill = await Bill.aggregate([
        {
            $match: {
                updatedAt: {
                    $gte: startOfMonth(new Date(req.query.date)),
                    $lte: endOfMonth(new Date(req.query.date))
                },
                parentOrganizationId,
                branchId: req.query.branchId,
                status: "paid",
                "claims.0": { $exists: false },
                "items.source.name": { $in: filter }
            }
        },
        {
            $unwind: "$items"
        },
        {
            $project: {
                patient: 1,
                "items.source.name": 1,
                totalOutPatientRevenue: {
                    $sum: {
                        $cond: [
                            {
                                $in: ["$items.source.name", filter]
                            },
                            "$items.total",
                            0
                        ]
                    }
                }
            }
        },
        {
            $group: {
                _id: null,
                totalRevenue: { $sum: "$totalOutPatientRevenue" }
            }
        },
        {
            $project: {
                _id: 0,
                totalRevenue: 1,
                hmoType: "private"
            }
        }
    ]);
    return claims.concat(bill);
};
