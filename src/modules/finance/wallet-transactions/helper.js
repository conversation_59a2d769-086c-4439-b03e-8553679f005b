import agenda from "config/agenda";
import JOB from "constants/jobs";
import { PAYMENT_METHOD } from "finance/payments/constants";
import { JOB_EXECUTION_TIME } from "helpers/constants";
import { formatCurrency, generateOTP } from "helpers/utils";
import RefundRequestMail from "mails/refund-request";
import WalletTransferOTP from "mails/wallet-transfer-otp";
import { Types } from "mongoose";
import { sendMail } from "services/mail";
import { uploadFile } from "services/storage";
import throwErrorWithCode from "utils/throw-error";
import { WALLET_TRANSACTION_PURPOSE, WALLET_TRANSACTION_STATUS } from "./constant";

const { ObjectId } = Types;

export const composeFilter = (query, parentOrganizationId) => {
    const { branchId, fromDate, toDate, purpose, _id, from, status, to, statuses } = query;
    const filter = { parentOrganizationId };
    if (branchId) filter.branchId = branchId;
    if (fromDate && toDate) filter.createdAt = { $gte: fromDate, $lte: toDate };
    if (purpose) filter.purpose = purpose;
    if (_id) filter._id = new ObjectId(_id);
    if (from) filter.from = new ObjectId(from);
    if (to) filter.to = new ObjectId(to);
    if (status) filter.status = status;
    if (statuses) filter.status = { $in: statuses };
    return filter;
};

export const composeTransferTransactions = (req, res) => {
    const { from, to } = req.body;
    const creditId = new ObjectId();
    const debitId = new ObjectId();
    const { _id: branchId, parentOrganizationId, profile: operator } = req.user.currentLocation;
    const { transferrer, recipient } = res.locals;
    const base = { ...req.body, branchId, parentOrganizationId, operator, status: "pending" };
    const pin = generateOTP(debitId.toHexString());
    const src = { type: "debit", from, purpose: "transfer", pin, _id: debitId, linkedTransaction: creditId };
    const dest = { type: "credit", to, method: "transfer", purpose: "fund", _id: creditId, linkedTransaction: debitId };
    return [
        { ...src, ...base, openingBalance: transferrer.wallet, closingBalance: transferrer.wallet },
        { ...dest, ...base, openingBalance: recipient.wallet, closingBalance: recipient.wallet }
    ];
};

export const composeWalletDoc = (req, res) => {
    const operator = req.user.currentLocation.profile;
    const { _id: branchId, parentOrganizationId } = req.user.currentLocation;
    const { mostRecentTransaction } = res.locals;

    const openingBalance = mostRecentTransaction?.closingBalance || 0;
    const closingBalance = openingBalance + req.body.amount;
    const lienOpeningBalance = mostRecentTransaction?.lienClosingBalance || 0;
    const lienClosingBalance = lienOpeningBalance;

    const doc = {
        ...req.body,
        to: req.params._id,
        type: "credit",
        operator,
        branchId,
        parentOrganizationId,
        openingBalance,
        closingBalance,
        lienOpeningBalance,
        lienClosingBalance,
        status: "completed"
    };
    return doc;
};

/**
 * Send OTP via SMS and/or Email for wallet transfer verification
 * @param {Object} params - Parameters for sending OTP
 * @param {string} params.phoneNumber - Patient's phone number
 * @param {string} params.email - Patient's email address
 * @param {Object} params.transaction - Transaction details
 * @param {Object} params.transferrer - Transferrer patient details
 * @param {Object} params.recipient - Recipient patient details
 * @param {Object} params.operator - Operator details
 * @param {Object} params.branch - Branch details
 * @param {Object} params.parentOrganization - Parent organization details
 */
export const sendOTP = async ({
    phoneNumber,
    email,
    transaction,
    transferrer,
    recipient,
    operator,
    branch,
    parentOrganization
}) => {
    if (!phoneNumber && !email) {
        throwErrorWithCode("Patient has no phone number or email address listed on their records", 409);
    }

    const amount = transaction.amount / 100;
    const pin = generateOTP(transaction._id.toString());
    const currency = branch?.currency?.symbol || "₦";
    const formattedAmount = `${currency}${amount.toLocaleString()}`;

    // Send SMS if phone number is available
    if (phoneNumber) {
        const smsMessage = `A transfer request of ${formattedAmount} was initiated from your wallet. Confirm with this OTP: ${pin}`;
        await agenda.schedule(JOB_EXECUTION_TIME, JOB.SMS, { to: phoneNumber, message: smsMessage });
    }

    // Send Email if email address is available
    if (email) {
        const emailBody = WalletTransferOTP({
            senderName: transferrer.name,
            amount: formattedAmount,
            receiverName: recipient.name,
            operatorName: operator.name,
            operatorLocation: branch.name,
            otpCode: pin,
            duration: "10 minutes",
            contactNumber: branch.phoneNumber || "N/A",
            contactEmail: branch.email || "<EMAIL>",
            hospitalName: parentOrganization.name
        });

        await sendMail({
            email: email,
            subject: "Wallet Transfer Verification Required",
            body: emailBody
        });
    }
};

export const composeWithdrawalDoc = async (req, res) => {
    const { mostRecentTransaction } = res.locals;
    const operator = req.user.currentLocation.profile;
    const { _id: branchId, parentOrganizationId } = req.user.currentLocation;
    const { amount } = req.body;
    let document;
    if (req.body.document) {
        document = await uploadFile(req.body.document, "withdrawal-requests");
    }

    // invariant: mostRecentTransaction.closingBalance >= amount.
    const openingBalance = mostRecentTransaction.closingBalance;
    const closingBalance = openingBalance - amount;

    const lienOpeningBalance = mostRecentTransaction.lienClosingBalance || 0;
    const lienClosingBalance = lienOpeningBalance + amount;

    const doc = {
        ...req.body,
        document,
        type: "debit",
        purpose: "withdrawal",
        operator,
        from: req.params._id,
        parentOrganizationId,
        branchId,
        openingBalance,
        closingBalance,
        lienOpeningBalance,
        lienClosingBalance,
        status: "pending"
    };
    return doc;
};

export const composeConfirmationQueries = (req, res) => {
    const operator = req.user.currentLocation.profile;
    const { from, to, amount } = res.locals.transaction;
    const transferringPxQuery = { $inc: { wallet: -amount }, $set: { operator } };
    const recipientPxQuery = { $inc: { wallet: amount }, $set: { operator } };
    const base = { operator, status: "completed" };
    const fromWallet = { $set: { ...base, openingBalance: from.wallet, closingBalance: from.wallet - amount } };
    const toWallet = { $set: { ...base, openingBalance: to.wallet, closingBalance: to.wallet + amount } };
    return { toWallet, recipientPxQuery, fromWallet, transferringPxQuery };
};

export const sendRefundRequestEmail = (voters, patient, transaction, currency) => {
    if (voters.length) {
        voters.forEach((voter) => {
            sendMail({
                email: voter.email,
                subject: "Refund Request Authorization",
                body: RefundRequestMail({
                    mrn: patient.mrn,
                    name: patient.name,
                    fullname: voter.name,
                    url: `${process.env.WEB_APP_URL}/billing/transactions?tab=Withdrawal`,
                    amount: formatCurrency(currency, Number(transaction.amount) / 100),
                    comment: transaction.comment,
                    attachment: transaction.document
                })
            });
        });
    }
};

export const baseRefundDoc = {
    method: PAYMENT_METHOD.CASH,
    status: WALLET_TRANSACTION_STATUS.COMPLETED,
    purpose: WALLET_TRANSACTION_PURPOSE.REFUND,
    transactionEntity: "Bill",
    type: "credit"
};
