import { error, success } from "helpers/response";
import {{pascalCase name}}Service from "./service";

export const create = async(req, res) => {
    try {
            const { profile: createdBy, parentOrganizationId } = req.user.currentLocation;
            const doc = { ...req.body, createdBy, updatedBy: createdBy, parentOrganizationId }
            const {{camelCase name}} = await {{pascalCase name}}Service.create(doc);
            return success(res, 201, {{camelCase name}});
        } catch(err) {
            return error(res, 500, err);
    }
};

export const update = async(req, res) => {
    try {
            const { profile: updatedBy } = req.user.currentLocation;
            const {{camelCase name}} = await {{pascalCase name}}Service.update(req.params._id, { ...req.body, updatedBy });
            return success(res, 200, {{camelCase name}});
        } catch(err) {
            return error(res, 500, err);
    }
};

export const getOne = async(req, res) => {
    try {
            const { parentOrganizationId } = req.user.currentLocation;
            const {{camelCase name}} = await {{pascalCase name}}Service.getOne({_id: req.params._id, parentOrganizationId });
            return success(res, 200, {{camelCase name}});
        } catch(err) {
            return error(res, 500, err);
    }
};

export const getMany = async(req, res) => {
    try {
            const { parentOrganizationId } = req.user.currentLocation;
            const {{camelCase name}} = await {{pascalCase name}}Service.getMany({ ...req.query, parentOrganizationId });
            return success(res, 200, {{camelCase name}});
        } catch(err) {
            return error(res, 500, err);
    }
};

export const toggle = async (req, res) => {
    try {
        const { profile: updatedBy } = req.user.currentLocation;
        const { recordIds, disabled } = req.body;
        await {{pascalCase name}}Service.bulkUpdate(recordIds, { disabled, updatedBy });
        return success(res, 200);
    } catch (err) {
        return error(res, 500, err);
    }
};
