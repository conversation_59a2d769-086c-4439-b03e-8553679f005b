import { idNameRequiredSchema } from "helpers/constants";
import { model, Schema } from "mongoose";
import paginator from "mongoose-paginate-v2";
import { composeIDSchema } from "utils/generate-id";

const modelName = "{{pascalCase name}}";

const schema = new Schema({
    _id: composeIDSchema(modelName),
    disabled: { type: Boolean, default: false, required: true },
    parentOrganizationId: { type: String, ref: "Organization" },
    createdBy: idNameRequiredSchema,
    updatedBy: idNameRequiredSchema
}, { timestamps: true });

schema.plugin(paginator);
const Model = model(modelName, schema);
export default Model;