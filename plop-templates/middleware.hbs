import Joi from "joi";
import { error } from "helpers/response";
import { paginationSchema } from "helpers/utils";
import {{pascalCase name}}Service from "./service";



export const createSchema = Joi.object({}).required()

export const validateCreateRequest = async(req, res, next) => {
    try {
        return next();
    } catch(err) {
        return error(res, 500, err);
    }
}

export const updateSchema = Joi.object({}).required()

export const validateUpdateRequest = async(req, res, next) => {
    try {
        return next();
    } catch(err) {
        return error(res, 500, err)
    }
}

export const getManySchema = Joi.object({
    ...paginationSchema
})

export const toggleSchema = Joi.object({
    recordIds: Joi.array().items(Joi.string().required()).required(),
    disabled: Joi.boolean().required()
}).required();

export const validateToggleRequest = async (req, res, next) => {
    try {
        const { parentOrganizationId } = req.user.currentLocation;
        const { recordIds } = req.body;
        const records = await {{pascalCase name}}Service.getMany(
            { _ids: recordIds, parentOrganizationId },
            { dontPaginate: true }
        );
        if (records.length !== recordIds.length) {
            return error(res, 404, "One/more records not found in DB. Recheck selection");
        }
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};