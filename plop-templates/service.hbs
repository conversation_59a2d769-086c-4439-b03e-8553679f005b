import {{pascalCase name}} from "./model";
import paginate from "utils/pagination";


const composeFilter = (queryParams) => {
    const filter = {};
    return filter;
}

const create = async(dto, session)  => {
    const doc = await new {{pascalCase name}}(dto).save({ session });
    return doc.toJSON();
}

const bulkCreate = async (dto) => {{pascalCase name}}.insertMany(dto);

const bulkUpdate = async (_ids, dto, session) => {{pascalCase name}}.updateMany({ _id: { $in: _ids } }, dto, { session });

const update = async(_id, dto, session) => {{pascalCase name}}.findByIdAndUpdate(_id, dto, { new: true, lean: true, session });

const getOne = async(filter, options) => {{pascalCase name}}.findOne(filter, {}, { lean: true, ...options });

const count = async(filter, options) => {{pascalCase name}}.countDocuments(filter, options);

const getMany =  async(dto, options = {})  => {
    const { page, limit, ...queryParams } = dto;
    const filter =  composeFilter(queryParams);
    if (options.dontPaginate) delete options.limit;
    return options.dontPaginate
        ? {{pascalCase name}}.find(filter, {}, { lean: true, ...options })
        : paginate({{pascalCase name}}.modelName, { filter, page, limit, ...options });
}

const bulkWrite = (writes, options = {}) => {{pascalCase name}}.bulkWrite(writes, { ordered: false, ...options });

const aggregate = (pipeline, options = {}) => {{pascalCase name}}.aggregate(pipeline, options);

const {{pascalCase name}}Service = { bulkCreate, bulkUpdate, bulkWrite, create, update, getOne, getMany, count, aggregate };

export default {{pascalCase name}}Service