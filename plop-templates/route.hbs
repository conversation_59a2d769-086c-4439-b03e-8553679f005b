import { Router } from "express";
import { isAuthorized } from "base/request";
import { validator } from "helpers/config";
import { create, update, getOne, getMany, toggle } from "./controller";
import { createSchema, updateSchema, validateCreateRequest, validateUpdateRequest, getManySchema, toggleSchema, validateToggleRequest } from "./middleware";


 
const router = Router();

router.post("/", isAuthorized(["super-create"]), validator.body(createSchema), validateCreateRequest, create);
router.patch("/:_id", isAuthorized(["super-edit"]), validator.body(updateSchema), validateUpdateRequest, update);
router.get("/:_id", isAuthorized(["super-view"]), getOne);
router.get("/", isAuthorized(["super-view"]), validator.query(getManySchema), getMany);
router.patch(
    "/",
    isAuthorized(["super-edit"]),
    validator.body(toggleSchema),
    validateToggleRequest,
    toggle
);

export default router;